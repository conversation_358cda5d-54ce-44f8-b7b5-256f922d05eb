# 表格重复检测功能

## 功能说明

在编辑状态下，如果指定的列数据有重复情况，在失去焦点时会给出提示，然后编辑的输入框重新获取焦点。

## 使用方法

### 1. 在列配置中启用重复检测

在表格的 `columns` 配置中，为需要检测重复的列添加 `checkDuplicate: true` 属性：

```javascript
const columns = [
  {
    prop: 'code',
    label: '编号',
    editor: {
      ctype: 'text',
      checkDuplicate: true // 启用重复检测
    }
  },
  {
    prop: 'name',
    label: '名称', 
    editor: {
      ctype: 'text'
      // 没有 checkDuplicate，不检测重复
    }
  }
]
```

### 2. 监听重复错误事件

在使用表格组件时，监听 `duplicate-error` 事件来处理重复错误：

```vue
<template>
  <!-- 错误提示 -->
  <el-alert
    v-if="duplicateError"
    :title="duplicateError.message"
    type="error"
    :closable="false"
    class="mb-4"
  />
  
  <base-table 
    v-model:data="tableData"
    :columns="columns"
    @duplicate-error="handleDuplicateError"
  />
</template>

<script setup>
import { ref } from 'vue'

const duplicateError = ref(null)

// 处理重复错误
const handleDuplicateError = (errorInfo) => {
  duplicateError.value = errorInfo
  
  // 3秒后自动清除错误提示
  setTimeout(() => {
    duplicateError.value = null
  }, 3000)
}
</script>
```

### 3. 错误信息结构

`duplicate-error` 事件传递的错误信息包含以下字段：

```javascript
{
  rowIndex: 0,           // 重复数据所在行索引
  colIndex: 1,           // 重复数据所在列索引
  column: {...},         // 列配置对象
  value: "A001",         // 重复的值
  message: "编号 的值 \"A001\" 已存在，请输入不同的值"  // 错误消息
}
```

## 功能特点

1. **自动重新聚焦**：检测到重复时，编辑框会自动重新获取焦点
2. **选中文本**：重新聚焦时会自动选中输入框中的文本，方便用户修改
3. **不退出编辑状态**：检测到重复时不会退出编辑状态，用户可以直接修改
4. **自定义错误消息**：提供清晰的错误提示信息
5. **灵活配置**：可以为不同列单独配置是否启用重复检测

## 示例页面

可以访问 `/duplicate-check-test` 页面查看完整的使用示例。

## 注意事项

1. 只有配置了 `editor.checkDuplicate: true` 的列才会进行重复检测
2. 空值（undefined、null、空字符串）不会被视为重复
3. 重复检测只在当前表格数据中进行，不包括空行
4. 建议配合错误提示组件使用，提供更好的用户体验
