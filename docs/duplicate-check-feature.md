# 表格重复检测功能

## 功能说明

在编辑状态下，如果指定的列数据有重复情况，在失去焦点或按回车键时会弹出提示框，用户点击确定后编辑的输入框会重新获取焦点。

## 使用方法

### 1. 在列配置中启用重复检测

在表格的 `columns` 配置中，为需要检测重复的列添加 `checkDuplicate: true` 属性：

```javascript
const columns = [
  {
    prop: 'code',
    label: '编号',
    editor: {
      ctype: 'text',
      checkDuplicate: true // 启用重复检测
    }
  },
  {
    prop: 'name',
    label: '名称', 
    editor: {
      ctype: 'text'
      // 没有 checkDuplicate，不检测重复
    }
  }
]
```

### 2. 使用表格组件

直接使用表格组件即可，重复检测功能会自动生效：

```vue
<template>
  <base-table
    v-model:data="tableData"
    :columns="columns"
    can-add
    :init-row="initRow"
  />
</template>

<script setup>
import { ref } from 'vue'
import BaseTable from '@/components/baseTable/index.vue'

const tableData = ref([
  { id: 1, code: 'A001', name: '项目1' },
  { id: 2, code: 'A002', name: '项目2' }
])
</script>
```

### 3. 弹框提示

当检测到重复数据时，系统会自动弹出警告提示框：

- **标题**：重复数据提示
- **内容**：显示具体的重复信息，如 "编号 的值 'A001' 已存在，请输入不同的值"
- **类型**：警告类型（warning）
- **按钮**：确定按钮

用户点击确定后，编辑框会重新获取焦点并选中文本。

## 功能特点

1. **弹框提示**：检测到重复时弹出警告提示框，用户体验友好
2. **失去焦点检测**：在编辑框失去焦点时自动检测重复
3. **回车键检测**：按回车键时也会进行重复检测
4. **自动重新聚焦**：用户点击确定后，编辑框会自动重新获取焦点
5. **选中文本**：重新聚焦时会自动选中输入框中的文本，方便用户修改
6. **不退出编辑状态**：检测到重复时不会退出编辑状态，用户可以直接修改
7. **清晰的错误消息**：提供具体的重复信息提示
8. **灵活配置**：可以为不同列单独配置是否启用重复检测

## 示例页面

可以访问 `/duplicate-check-test` 页面查看完整的使用示例。

## 注意事项

1. 只有配置了 `editor.checkDuplicate: true` 的列才会进行重复检测
2. 空值（undefined、null、空字符串）不会被视为重复
3. 重复检测只在当前表格数据中进行，不包括空行
4. 建议配合错误提示组件使用，提供更好的用户体验
