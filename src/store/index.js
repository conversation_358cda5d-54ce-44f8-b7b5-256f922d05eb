import { defineStore } from "pinia"
import { deepClone, validatePosition, formatNumber, isEmpty } from "@/common/js/dataUtils"
import { parsePCDString } from "@/common/js/pcdParser"
import { request } from "@/common/js/request"
const pathMap = new Map()

export const useStore = defineStore("useStore", {
  state: () => {
    return {
      open: false,
      records: [],
      recordRankList: [],
      pointCloundPath: [],
      pointCloundUpdate: false,
      calculatePara: {
        shipLength: undefined,
        heel: [],
        trim: [],
      },
      patchPara: {
        auto: {
          check: false,
          tol: undefined,
          width: undefined,
        },
        corrugate: {
          check: false,
          data: [
            // {
            //   dir: 0, // x y z
            //   type: 0, //水平 垂直
            //   width: 10.0,
            //   depth: 10.0,
            //   webwidth: 5.0,
            // },
          ],
        },
        custom: {
          check: true,
          data: [
            // {
            //   dir: 0,
            //   spos: 0,
            //   epos: 10.0,
            //   spoint: "0,1", // x y
            //   epoint: "0,1",
            // },
          ],
        },
      },
      sectionpos: [
        // {
        //   pos: 1.0,
        //   posfrom: 2.0, // 为null或者0就是avg0
        //   posavg: 1.5,
        //   avg: false,
        // },
      ],
      measureData: {
        capacitypos: {
          spos: undefined,
          epos: undefined,
          step: undefined,
        },
        component: [
          // {
          //   w1: 1.0,
          //   w2: 1.0,
          //   L: 1.0,
          //   H: 1.0,
          //   count: 1.0,
          //   sz: 1.0,
          //   ez: 1.0,
          //   v: 1.0,
          // },
        ],
        ruler: {
          type: undefined,
          H: undefined,
          x: undefined,
          y: undefined,
          z: undefined,
          h: undefined,
          point: [
            // {
            //   x: 0,
            //   y: 0,
            //   z: 0,
            // },
          ],
        },
      },
      mainTanks: [
        // {
        //   tankfullname: "T1",
        //   tankname: "T1",
        // },
      ],
      tanks: [
        // {
        //   tankfullname: "T1",
        //   tankname: "T1",
        //   defineMethod: 0,
        // },
      ],
      currentTank: {
        // tankfullname: "T1",
        // tankname: "T1",
        // definetype: 0,
      },
      boundarypos: [],
      boundarypoint: [],

      boundarypointPath: "",
      boundarypointData: [],
      sectionBoundary: [], // 轮廓点数据
      permission: [true, true], // 模型 、 计算
      bendPos: [],
      quickCalc: {
        calcType: "heel",
        heel: {
          start: -5,
          end: 5,
          step: 1,
        },
        trim: {
          start: -0.3,
          end: 0.4,
          step: 0.1,
        },
      },
    }
  },
  getters: {
    hasTank() {
      return this.tanks.length > 0
    },
  },
  actions: {
    async newProject(data) {
      await request("TC_VtoM_NewProject", data)
      this.open = true
    },
    async openProject() {
      await this.loadProject()
      this.open = true
    },
    async closeProject() {
      await request("TC_VtoM_CloseProject", {})
      this.open = false
      this.records = []
      this.tanks = []
    },
    async loadProject() {
      await request("TC_VtoM_LoadProject", {})
      await this.loadTankList()
    },
    async addTank() {
      await request("TC_VtoM_AddTank", {})
    },
    async newTank(tank) {
      await request("TC_VtoM_NewTank", {
        tankinfo: tank,
      })
      await this.loadTankList()
    },
    async loadTankInfo(tank) {
      const res = await request("TC_VtoM_LoadTankInfo", {
        tankname: tank.tankname,
      })
      this.currentTank = res.tankinfo
      this.boundarypos = []
      this.boundarypoint = []
    },
    async updateTank(tank) {
      await request("TC_VtoM_UpdateTankInfo", {
        tankinfo: tank,
      })
      await this.loadTankList()
      this.currentTank = tank
    },
    async removeTank(tank) {
      await request("TC_VtoM_DeleteTank", {
        tankname: tank.tankname,
      })
      this.loadTankList()
    },
    async loadTankList() {
      const res = await request("TC_VtoM_LoadTankList", {})

      this.tanks = res.tank
    },
    async loadMainTank() {
      const res = await request("TC_VtoM_LoadMainTank", {})

      this.mainTanks = res.tank
    },
    async loadRecords(tankname) {
      this.records = []
      const res = await request("TC_VtoM_LoadRecords", {
        tankname,
      })
      this.records = res.record
    },
    async loadRecordTankList() {
      this.recordRankList = []
      const res = await request("TC_VtoM_LoadRecordTankList", {})
      this.recordRankList = res?.tankname || []
    },
    // 导入工程
    async importProject() {
      await request("TC_VtoM_ImportProject", {})
      await this.loadTankList()
      this.open = true
    },
    // 导入测量数据
    async importMeasureData() {
      await request("TC_VtoM_ImportMeasureData", {})
    },
    async save() {
      await request("TC_VtoM_Save", {})
    },

    async loadPointCloud(tank) {
      const res = await request("TC_VtoM_LoadPointCloud", {
        tankname: tank.tankname,
      })
      this.pointCloundPath = res.path
      this.pointCloundUpdate = res.update[0]
    },
    async loadCalculatePara() {
      const res = await request("TC_VtoM_LoadCalculatePara", {})

      const calculatePara = res || {}

      this.calculatePara = {
        shipLength: calculatePara.shipLength,
      }
      ;["heel", "trim"].forEach((key) => {
        if (calculatePara[key] && calculatePara[key].length > 0) {
          this.calculatePara[key] = calculatePara[key].map((item) => {
            return {
              value: item,
            }
          })
        }
      })
    },
    async updateCalculatePara(data) {
      await request("TC_VtoM_UpdateCalculatePara", data)
    },
    async loadPatchPara(data) {
      const res = await request("TC_VtoM_LoadPatchPara", data)

      let para = res.para

      if (!para.custom.data) {
        para.custom.data = []
      }

      if (!para.corrugate.data) {
        para.corrugate.data = []
      }
      if (para.custom.data) {
        para.custom.data.map((item) => {
          if (Array.isArray(item.spoint)) {
            item.spoint = item.spoint.map((v) => formatNumber(v, 1)).join(",")
          }
          if (Array.isArray(item.epoint)) {
            item.epoint = item.epoint.map((v) => formatNumber(v, 1)).join(",")
          }
          item.spos = formatNumber(item.spos, 1)
          item.epos = formatNumber(item.epos, 1)
          return item
        })
      }

      this.patchPara = para
    },
    async updatePatchPara(data) {
      await request("TC_VtoM_UpdatePatchPara", data)
    },
    async loadSectionPos(data) {
      const res = await request("TC_VtoM_LoadSectionPos", data)

      const sectionpos = res.sectionpos || []
      this.sectionpos = sectionpos.map((item) => {
        item.posavg = formatNumber(item.posavg, 1)
        item.posfrom = formatNumber(item.posfrom, 1)
        item.pos = formatNumber(item.pos, 1)
        return item
      })
    },

    async updateSectionPos(data) {
      await request("TC_VtoM_UpdateSectionPos", data)
    },
    async calSectionPosAvg(data) {
      const res = await request("TC_VtoM_CalSectionPosAvg", data)
      return res.avg
    },

    async loadBoundaryPos(data) {
      const res = await request("TC_VtoM_LoadBoundaryPos", data)
      const boundarypos = res.boundarypos || []

      this.boundarypos = boundarypos.map((item) => {
        item.pos = formatNumber(item.pos, 1)
        return item
      })
    },

    async updateBoundaryPos(data) {
      await request("TC_VtoM_UpdateBoundaryPos", data)
    },

    async loadSectionBoundary(data) {
      const res = await request("TC_VtoM_LoadSectionBoundary", data)
      const boundarypoint = res.boundarypoint || []
      this.boundarypoint = boundarypoint.map((item) => {
        item.point = item.point.map((val) => formatNumber(val, 1)).join(",")
        return item
      })

      if (import.meta.env.DEV) {
        fetch("/test.pcd").then((res) => {
          res.text().then((text) => {
            const result = parsePCDString(text)
            this.boundarypointData = result.data.positions
          })
        })
      }

      this.boundarypointData = []
      if (res.path) {
        const pathData = await request("loadPath", {
          path: res.path,
        })

        const result = parsePCDString(pathData)
        const positions = result.data.positions || []
        this.boundarypointData = positions
      }
    },
    async calTankBoundary(data) {
      await request("TC_VtoM_CalTankBoundary", data)
    },

    async updateSectionBoundary(data) {
      const cloneData = deepClone(data)

      cloneData.boundary.forEach((item) => {
        item.boundarypoint = item.boundarypoint
          .filter((point) => {
            return point.point && validatePosition(point.point)
          })
          .map((point) => {
            point.point = point.point.split(",").map(Number)

            point.bend = point.bend || false
            return point
          })
      })

      await request("TC_VtoM_UpdateSectionBoundary", cloneData)
    },

    async loadMeasureData(data) {
      const res = await request("TC_VtoM_LoadMeasureData", data)

      // 格式化体积数据为3位小数
      if (res.component && Array.isArray(res.component)) {
        res.component = res.component.map((item) => {
          Object.keys(item).forEach((key) => {
            if (key === "v") {
              item[key] = formatNumber(item[key], 3)
            } else {
              item[key] = formatNumber(item[key], 1)
            }
          })
          return item
        })
      }
      if (!isEmpty(res.capacitypos.epos)) {
        res.capacitypos.epos = formatNumber(res.capacitypos.epos, 1)
      }
      if (!isEmpty(res.capacitypos.spos)) {
        res.capacitypos.spos = formatNumber(res.capacitypos.spos, 1)
      }
      if (!isEmpty(res.capacitypos.step)) {
        res.capacitypos.step = formatNumber(res.capacitypos.step, 1)
      }

      if (res.ruler) {
        if (!isEmpty(res.ruler.H)) {
          res.ruler.H = formatNumber(res.ruler.H, 1)
        }
        if (!isEmpty(res.ruler.x)) {
          res.ruler.x = formatNumber(res.ruler.x, 1)
        }
        if (!isEmpty(res.ruler.y)) {
          res.ruler.y = formatNumber(res.ruler.y, 1)
        }
        if (!isEmpty(res.ruler.z)) {
          res.ruler.z = formatNumber(res.ruler.z, 1)
        }
        if (!isEmpty(res.ruler.h)) {
          res.ruler.h = formatNumber(res.ruler.h, 1)
        }
        if (res.ruler.point) {
          res.ruler.point = res.ruler.point.map((item) => {
            item.x = formatNumber(item.x, 1)
            item.y = formatNumber(item.y, 1)
            item.z = formatNumber(item.z, 1)
            return item
          })
        }
      }

      this.measureData = res
    },
    async updatePointCloud(data) {
      const res = await request("TC_VtoM_UpdatePointCloud", data)

      this.pointCloundPath = res.path
    },
    async loadPath(path, update = false) {
      if (pathMap.get(path) && !update) {
        return pathMap.get(path)
      }

      // const pathData = await window.Channel.loadPath({
      //   path,
      // })

      const pathData = await request("loadPath", {
        path,
      })

      const encoder = new TextEncoder()

      const arrayData = encoder.encode(pathData)

      pathMap.set(path, arrayData)

      return arrayData
    },
    setCurrentTank(tank) {
      this.currentTank = tank
    },

    async loadTankBoundary(data) {
      const res = await request("TC_VtoM_LoadTankBoundary", data)

      this.sectionBoundary = res.section?.filter((item) => item) || []
    },

    async updateMainTank(data) {
      await request("TC_VtoM_UpdateMainTank", data)
    },

    async export(data) {
      await request("TC_VtoM_Export", data)
    },

    async loadPermission() {
      const res = await request("TC_VtoM_Permission", {})
      this.permission = res.permission
    },

    async calTankPatch(data) {
      await request("TC_VtoM_CalTankPatch", data)
    },
    async calBendPos(data) {
      const res = await request("TC_VtoM_CalBendPos", data)
      this.bendPos = res.pos.map((item) => formatNumber(item, 1))
    },
    async calTankPosAvg(data) {
      await request("TC_VtoM_CalTankPosAvg", data)
    },
    async calCapacity(data) {
      await request("TC_VtoM_CalCapacity", data)
    },

    async closeTank() {
      await request("TC_VtoM_CloseTank", {
        tankname: this.currentTank.tankname,
      })
    },
  },
})
