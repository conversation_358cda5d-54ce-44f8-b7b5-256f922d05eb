import { defineStore } from "pinia"

export const useProgressStore = defineStore("progress", {
  state: () => ({
    isVisible: false,
    progress: 0,
    text: "",
    timer: null,
  }),

  actions: {
    // 显示进度条
    show(initialProgress = 0, initialText = "") {
      this.isVisible = true
      this.progress = initialProgress
      this.text = initialText
    },

    // 隐藏进度条
    hide() {
      this.isVisible = false
      this.progress = 0
      this.text = ""
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    // 设置进度
    setProgress(value, text = "") {
      this.progress = Math.max(0, Math.min(100, value))
      if (text) {
        this.text = text
      }
    },

    // 设置文本
    setText(text) {
      this.text = text
    },

    // 增加进度
    increment(value = 1, text = "") {
      this.setProgress(this.progress + value, text)
    },

    // 模拟进度（用于演示或不确定进度的任务）
    simulate(duration = 3000, text = "处理中...") {
      this.show(0, text)

      const steps = 100
      const interval = duration / steps
      let currentStep = 0

      this.timer = setInterval(() => {
        currentStep++
        const progress = (currentStep / steps) * 90 // 最多到90%，留10%给完成状态
        this.setProgress(progress)

        if (currentStep >= steps) {
          clearInterval(this.timer)
          this.timer = null
        }
      }, interval)
    },

    // 完成进度
    complete(text = "完成") {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }

      this.setProgress(100, text)

      // 1秒后自动隐藏
      setTimeout(() => {
        this.hide()
      }, 1000)
    },

    // 错误状态
    error(text = "处理失败") {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }

      this.setText(text)

      // 2秒后自动隐藏
      setTimeout(() => {
        this.hide()
      }, 2000)
    },

    // 重置
    reset() {
      this.hide()
    },
  },
})

// 导出便捷方法
export const progressBar = {
  show: (progress = 0, text = "") => {
    const store = useProgressStore()
    store.show(progress, text)
  },

  hide: () => {
    const store = useProgressStore()
    store.hide()
  },

  setProgress: (value, text = "") => {
    const store = useProgressStore()
    store.setProgress(value, text)
  },

  setText: (text) => {
    const store = useProgressStore()
    store.setText(text)
  },

  increment: (value = 1, text = "") => {
    const store = useProgressStore()
    store.increment(value, text)
  },

  simulate: (duration = 3000, text = "处理中...") => {
    const store = useProgressStore()
    store.simulate(duration, text)
  },

  complete: (text = "完成") => {
    const store = useProgressStore()
    store.complete(text)
  },

  error: (text = "处理失败") => {
    const store = useProgressStore()
    store.error(text)
  },

  reset: () => {
    const store = useProgressStore()
    store.reset()
  },
}
