import { createApp } from "vue"
import App from "./app.vue"
import "normalize.css"
import "./styles/global.scss"
import "uno.css"
import router from "./router"
import "element-plus/dist/index.css"
import "./styles/element.scss"
import "virtual:svg-icons-register"
import FontAwesome from "./plugins/fontawesome"
import { setupElementPlus } from "./plugins/element-plus"
import "@/common/js/channel"
import { createPinia } from "pinia"

const app = createApp(App)
app.use(router)
app.use(createPinia())
app.use(FontAwesome)
setupElementPlus(app)
app.mount("#app")
