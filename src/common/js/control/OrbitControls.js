import * as THREE from "three"
import SpinControls from "./SpinControls"
// import rotationUrl from '@/assets/Icon/rotation.png';
// import locationUrl from '@/assets/Icon/location.png';

// OrbitControls performs orbiting, dollying (zooming), and panning.
// Unlike TrackballControls, it maintains the "up" direction object.up (+Y by default).
//
//    Orbit - left mouse / touch: one-finger move
//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish
//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move

const _changeEvent = { type: "change" }
const _startEvent = { type: "start" }
const _endEvent = { type: "end" }
const _ray = new THREE.Ray()
const _plane = new THREE.Plane()
const _TILT_LIMIT = Math.cos(70 * THREE.MathUtils.DEG2RAD)

function transformTo(outParentToTarget, parent, target) {
  outParentToTarget.copy(parent) // 目标的矩阵  0 0 100
  outParentToTarget.invert() // 目标的矩阵的逆 0 0
  outParentToTarget.multiply(target) // 相机的矩阵
}

class OrbitControls extends THREE.EventDispatcher {
  constructor(object, domElement) {
    super()

    this.object = object
    this.domElement = domElement
    this.domElement.style.touchAction = "none" // disable touch scroll

    // Set to false to disable this control
    this.enabled = true

    // "target"设置焦点位置，即物体绕其轨道运行的位置
    this.targetObj = new THREE.Object3D()
    this.target = this.targetObj.position

    this.targetObj.lookAt(object.position)

    this.startTrackballScreenCenter = true
    this.trackballToObject = new THREE.Matrix4()

    this.targetObj.updateWorldMatrix(true, false)
    this.object.updateWorldMatrix(true, false)
    transformTo(this.trackballToObject, this.targetObj.matrixWorld, this.object.matrixWorld)

    // Sets the 3D cursor (similar to Blender), from which the maxTargetRadius takes effect
    this.cursor = new THREE.Vector3()

    // How far you can dolly in and out ( PerspectiveCamera only )
    this.minDistance = 0
    this.maxDistance = Infinity

    // 正交相机模式下的最小最大距离，用于避免某些bug的产生
    this.minDistanceOrth = 0
    this.maxDistanceOrth = Infinity

    // How far you can zoom in and out ( OrthographicCamera only )
    this.minZoom = 0
    this.maxZoom = Infinity

    // Limit camera target within a spherical area around the cursor
    this.minTargetRadius = 0
    this.maxTargetRadius = Infinity

    // How far you can orbit vertically, upper and lower limits.
    // Range is 0 to Math.PI radians.
    this.minPolarAngle = 0 // radians
    this.maxPolarAngle = Math.PI // radians

    // How far you can orbit horizontally, upper and lower limits.
    // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )
    this.minAzimuthAngle = -Infinity // radians
    this.maxAzimuthAngle = Infinity // radians

    // Set to true to enable damping (inertia)
    // If damping is enabled, you must call controls.update() in your animation loop
    this.enableDamping = false
    this.dampingFactor = 0.05

    // This option actually enables dollying in and out; left as "zoom" for backwards compatibility.
    // Set to false to disable zooming
    this.enableZoom = true
    this.zoomSpeed = 1.0

    // Set to false to disable rotating
    this.enableRotate = true
    this.rotateSpeed = 1.0

    // Set to false to disable panning
    this.enablePan = true
    this.panSpeed = 1.0
    this.screenSpacePanning = true // if false, pan orthogonal to world-space direction camera.up
    this.keyPanSpeed = 7.0 // pixels moved per arrow key push
    this.zoomToCursor = true

    // Set to true to automatically rotate around the target
    // If auto-rotate is enabled, you must call controls.update() in your animation loop
    this.autoRotate = false
    this.autoRotateSpeed = 2.0 // 30 seconds per orbit when fps is 60

    // The four arrow keys
    this.keys = {
      LEFT: "ArrowLeft",
      UP: "ArrowUp",
      RIGHT: "ArrowRight",
      BOTTOM: "ArrowDown",
    }

    // Mouse buttons
    this.mouseButtons = {
      LEFT: THREE.MOUSE.ROTATE,
      MIDDLE: THREE.MOUSE.DOLLY,
      RIGHT: THREE.MOUSE.PAN,
    }

    // Touch fingers
    this.touches = {
      ONE: THREE.TOUCH.ROTATE,
      TWO: THREE.TOUCH.DOLLY_PAN,
    }

    // for reset
    this.target0 = this.target.clone()
    this.position0 = this.object.position.clone()
    this.zoom0 = this.object.zoom

    // the target DOM element for key events
    this._domElementKeyEvents = null

    //
    // public methods
    //

    this.setFixedTarget = function (target) {
      fixedTarget = target
      if (fixedTarget) {
        scope.setTargetPosition(target)
      }
    }

    this.setCamera = function (camera) {
      if (camera instanceof THREE.PerspectiveCamera) {
        scope.object = camera
        scope.spinControl.camera = camera
        scope.spinControl.setPointerToSphereMapping(scope.spinControl.POINTER_SPHERE_MAPPING.RAYCAST)
      } else if (camera instanceof THREE.OrthographicCamera) {
        scope.object = camera
        scope.spinControl.camera = camera
        scope.spinControl.setPointerToSphereMapping(scope.spinControl.POINTER_SPHERE_MAPPING.HOLROYD)
      } else {
        console.error("Not instanceof THREE.PerspectiveCamera or THREE.OrthographicCamera")
      }
    }

    this.getPolarAngle = function () {
      return spherical.phi
    }

    this.getAzimuthalAngle = function () {
      return spherical.theta
    }

    this.getDistance = function () {
      return this.object.position.distanceTo(this.target)
    }

    //TODO:临时注释，解决打包报错问题
    // this.listenToKeyEvents = function (domElement) {
    //     domElement.addEventListener('keydown', onKeyDown);
    //     this._domElementKeyEvents = domElement;
    // };

    //TODO:临时注释，解决打包报错问题
    // this.stopListenToKeyEvents = function () {
    //     this._domElementKeyEvents.removeEventListener('keydown', onKeyDown);
    //     this._domElementKeyEvents = null;
    // };

    this.saveState = function () {
      scope.target0.copy(scope.target)
      scope.position0.copy(scope.object.position)
      scope.zoom0 = scope.object.zoom
    }

    this.reset = function () {
      scope.target.copy(scope.target0)
      scope.object.position.copy(scope.position0)
      scope.object.zoom = scope.zoom0

      scope.object.updateProjectionMatrix()
      scope.dispatchEvent(_changeEvent)

      scope.update()

      state = STATE.NONE
    }

    this.movedTarget = (function () {
      var v = new THREE.Vector3()

      return function movedTarget() {
        scope.targetObj.updateWorldMatrix(true, false)
        scope.object.updateWorldMatrix(true, false)
        transformTo(scope.trackballToObject, scope.targetObj.matrixWorld, scope.object.matrixWorld)

        // restrict radius to be between desired limits
        v.setFromMatrixPosition(scope.trackballToObject)

        if (scope.object.isOrthographicCamera) {
          v.clampLength(v.length(), scope.maxDistance)
        } else {
          v.multiplyScalar(scale)
          v.clampLength(scope.minDistance, scope.maxDistance)
        }

        scope.trackballToObject.setPosition(v)

        scope.object.matrix.copy(scope.targetObj.matrixWorld)
        scope.object.matrix.multiply(scope.trackballToObject)
        this.adjustTrackballRadius()
      }
    })()

    this.setTargetPosition = function (positionVector) {
      if (fixedTarget) {
        scope.target.copy(fixedTarget)
      } else {
        scope.target.copy(positionVector)
      }
      this.movedTarget()
    }

    this.setRotateSensitivity = function (sensitivity) {
      // 考虑到相机反转所以需要相反数
      scope.spinControl.rotateSensitivity = -1 * sensitivity
    }

    // this method is exposed, but perhaps it would be better if we can make it private...
    this.update = (function () {
      const lastPosition = new THREE.Vector3()
      const lastQuaternion = new THREE.Quaternion()
      const lastTargetPosition = new THREE.Vector3()
      const _v = new THREE.Vector3()

      return function update() {
        const position = scope.object.position // 相机坐标

        scope.spinControl.update()
        // move target to panned location
        if (scope.enableDamping === true) {
          scope.target.addScaledVector(panOffset, scope.dampingFactor)
        } else {
          scope.target.add(panOffset)
        }

        scope.targetObj.updateWorldMatrix(true, false)
        scope.object.updateWorldMatrix(true, false)

        _v.setFromMatrixPosition(scope.trackballToObject)

        _v.multiplyScalar(scope.object.isOrthographicCamera ? Math.pow(scale, 2) : scale)

        // restrict radius to be between desired limits
        _v.clampLength(scope.minDistance, scope.maxDistance)

        if (scope.object.isOrthographicCamera) {
          if (_v.length() < scope.minDistanceOrth) {
            _v.clampLength(scope.minDistanceOrth * 1.1, scope.maxDistance)
          }
        }

        scope.trackballToObject.setPosition(_v)
        scope.object.matrix.copy(scope.targetObj.matrixWorld)
        scope.object.matrix.multiply(scope.trackballToObject)

        scope.adjustTrackballRadius()
        scope.object.matrix.decompose(position, scope.object.quaternion, scope.object.scale)

        // Limit the target distance from the cursor to create a sphere around the center of interest
        scope.target.sub(scope.cursor)
        scope.target.clampLength(scope.minTargetRadius, scope.maxTargetRadius)
        scope.target.add(scope.cursor)

        let zoomChanged = false

        // 更新平移
        if (scope.enableDamping === true) {
          panOffset.multiplyScalar(1 - scope.dampingFactor)
        } else {
          panOffset.set(0, 0, 0)
        }

        // adjust camera position
        if (scope.zoomToCursor && performCursorZoom) {
          let newRadius = null
          if (scope.object.isPerspectiveCamera) {
            // move the camera down the pointer ray
            // scope method avoids floating point error
            const prevRadius = _v.length()
            newRadius = clampDistance(prevRadius * scale)

            const radiusDelta = prevRadius - newRadius
            position.addScaledVector(dollyDirection, radiusDelta)
            scope.object.updateMatrixWorld()

            zoomChanged = !!radiusDelta
          } else if (scope.object.isOrthographicCamera) {
            // adjust the ortho camera position based on zoom changes
            const mouseBefore = new THREE.Vector3(mouse.x, mouse.y, 0)
            mouseBefore.unproject(scope.object)

            const prevZoom = scope.object.zoom
            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))
            scope.object.updateProjectionMatrix()

            zoomChanged = prevZoom !== scope.object.zoom

            const mouseAfter = new THREE.Vector3(mouse.x, mouse.y, 0)
            mouseAfter.unproject(scope.object)
            position.sub(mouseAfter).add(mouseBefore)
            scope.object.updateMatrixWorld()

            newRadius = _v.length()
          } else {
            console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.")
            scope.zoomToCursor = false
          }

          // handle the placement of the target
          if (newRadius !== null) {
            if (scope.screenSpacePanning) {
              // position the orbit target in front of the new camera position
              scope.target.set(0, 0, -1).transformDirection(scope.object.matrix).multiplyScalar(newRadius).add(position)
            } else {
              // get the ray and translation plane to compute target
              _ray.origin.copy(position)
              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix)

              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid
              // extremely large values
              if (Math.abs(scope.object.up.dot(_ray.direction)) < _TILT_LIMIT) {
                scope.object.lookAt(scope.target)
              } else {
                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target)
                _ray.intersectPlane(_plane, scope.target)
              }
            }
          }
        } else if (scope.object.isOrthographicCamera) {
          const prevZoom = scope.object.zoom
          scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))

          if (prevZoom !== scope.object.zoom) {
            scope.object.updateProjectionMatrix()
            zoomChanged = true
          }
        }

        scale = 1
        performCursorZoom = false

        // rotation angle update condition is:
        // min(camera displacement, camera rotation in radians)^2 > EPS
        // using small-angle approximation cos(x/2) = 1 - x^2 / 8
        if (zoomChanged || lastPosition.distanceToSquared(position) > EPS || 8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS || lastTargetPosition.distanceToSquared(scope.target) > EPS) {
          scope.dispatchEvent(_changeEvent)

          lastPosition.copy(position)
          lastQuaternion.copy(scope.object.quaternion)
          lastTargetPosition.copy(scope.target)
          zoomChanged = false

          // Don't let camera movement cause mouse to move over sphere across frames
          scope.spinControl.resetInputAfterCameraMovement()

          return true
        }

        return false
      }
    })()

    this.onWindowResize = function () {
      scope.spinControl.onWindowResize()
      scope.adjustTrackballRadius()
    }

    // 根据相机调整跟踪球半径
    this.adjustTrackballRadius = (function () {
      var TRACKBALL_PERCENT_OF_SCREEN = 2
      var v = new THREE.Vector3()
      var cameraToTrackball = new THREE.Matrix4()

      return function adjustTrackballRadius() {
        if (scope.object.isPerspectiveCamera) {
          var limitingFov = Math.min(scope.object.fov, scope.object.fov * scope.object.aspect)
          var distanceToScreenSize = Math.sin(((limitingFov / 2) * Math.PI) / 180.0)

          transformTo(cameraToTrackball, scope.object.matrix, scope.targetObj.matrixWorld)
          v.setFromMatrixPosition(cameraToTrackball)
          scope.spinControl.trackballRadius = TRACKBALL_PERCENT_OF_SCREEN * v.length() * distanceToScreenSize
        } else {
          // assume orthographic camera

          var limitingDimension = Math.min(scope.object.right - scope.object.left, scope.object.top - scope.object.bottom) / scope.object.zoom
          scope.spinControl.trackballRadius = (TRACKBALL_PERCENT_OF_SCREEN / 2) * limitingDimension
        }
      }
    })()

    this.setRotateButton = function (button) {
      switch (button) {
        case 0:
          scope.mouseButtons.LEFT = THREE.MOUSE.ROTATE
          scope.mouseButtons.MIDDLE = THREE.MOUSE.DOLLY
          scope.mouseButtons.RIGHT = THREE.MOUSE.PAN
          break
        case 1:
          scope.mouseButtons.LEFT = THREE.MOUSE.DOLLY
          scope.mouseButtons.MIDDLE = THREE.MOUSE.ROTATE
          scope.mouseButtons.RIGHT = THREE.MOUSE.PAN
          break
        case 2:
          scope.mouseButtons.LEFT = THREE.MOUSE.PAN
          scope.mouseButtons.MIDDLE = THREE.MOUSE.DOLLY
          scope.mouseButtons.RIGHT = THREE.MOUSE.ROTATE
          break
      }
      scope.spinControl.mouseAction = button
    }

    this.enable = function (enabled) {
      scope.enabled = enabled
      scope.spinControl.enable(enabled)
    }

    this.dispose = function () {
      scope.domElement.removeEventListener("contextmenu", onContextMenu)

      scope.domElement.removeEventListener("pointerdown", onPointerDown)
      scope.domElement.removeEventListener("pointercancel", onPointerUp)
      scope.domElement.removeEventListener("wheel", onMouseWheel)

      scope.domElement.removeEventListener("pointermove", onPointerMove)
      scope.domElement.removeEventListener("pointerup", onPointerUp)

      const document = scope.domElement.getRootNode() // offscreen canvas compatibility

      document.removeEventListener("keydown", interceptControlDown, {
        capture: true,
      })

      if (scope._domElementKeyEvents !== null) {
        scope._domElementKeyEvents.removeEventListener("keydown", onKeyDown)
        scope._domElementKeyEvents = null
      }

      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?
    }

    //
    // internals
    //

    const scope = this

    const STATE = {
      NONE: -1,
      ROTATE: 0,
      DOLLY: 1,
      PAN: 2,
      TOUCH_ROTATE: 3,
      TOUCH_PAN: 4,
      TOUCH_DOLLY_PAN: 5,
      TOUCH_DOLLY_ROTATE: 6,
    }

    scope.STATE = STATE

    let state = STATE.NONE

    const EPS = 0.000001

    // current position in spherical coordinates
    const spherical = new THREE.Spherical()
    const sphericalDelta = new THREE.Spherical()

    let scale = 1
    const panOffset = new THREE.Vector3()

    const rotateStart = new THREE.Vector2()
    const rotateEnd = new THREE.Vector2()
    const rotateDelta = new THREE.Vector2()

    const panStart = new THREE.Vector2()
    const panEnd = new THREE.Vector2()
    const panDelta = new THREE.Vector2()

    const dollyStart = new THREE.Vector2()
    const dollyEnd = new THREE.Vector2()
    const dollyDelta = new THREE.Vector2()

    const dollyDirection = new THREE.Vector3()
    const mouse = new THREE.Vector2()
    let performCursorZoom = false

    const pointers = []
    const pointerPositions = {}

    let controlActive = false

    let fixedTarget = undefined

    /*******
     * @description: 相机是否处于平移或旋转状态
     * @return {*}
     */
    this.isPanningOrRotating = function () {
      return state == STATE.PAN || state == STATE.ROTATE || state == STATE.TOUCH_PAN || state == STATE.TOUCH_ROTATE || state == STATE.TOUCH_DOLLY_PAN || state == STATE.TOUCH_DOLLY_ROTATE
    }

    function getAutoRotationAngle(deltaTime) {
      if (deltaTime !== null) {
        return ((2 * Math.PI) / 60) * scope.autoRotateSpeed * deltaTime
      } else {
        return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed
      }
    }

    function getZoomScale(delta) {
      const normalizedDelta = Math.abs(delta * 0.01)
      return Math.pow(0.95, scope.zoomSpeed * normalizedDelta)
    }

    function rotateLeft(angle) {
      sphericalDelta.theta -= angle
    }

    function rotateUp(angle) {
      sphericalDelta.phi -= angle
    }

    const panLeft = (function () {
      const v = new THREE.Vector3()

      return function panLeft(distance, objectMatrix) {
        v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix
        v.multiplyScalar(-distance)
        panOffset.add(v)
      }
    })()

    const panUp = (function () {
      const v = new THREE.Vector3()

      return function panUp(distance, objectMatrix) {
        if (scope.screenSpacePanning === true) {
          v.setFromMatrixColumn(objectMatrix, 1)
        } else {
          v.setFromMatrixColumn(objectMatrix, 0)
          v.crossVectors(scope.object.up, v)
        }

        v.multiplyScalar(distance)

        panOffset.add(v)
      }
    })()

    // deltaX and deltaY are in pixels; right and down are positive
    const pan = (function () {
      const offset = new THREE.Vector3()

      return function pan(deltaX, deltaY) {
        const element = scope.domElement

        if (scope.object.isPerspectiveCamera) {
          // perspective
          const position = scope.object.position
          offset.copy(position).sub(scope.target)
          let targetDistance = offset.length()

          // half of the fov is center to top of screen
          targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0)

          // we use only clientHeight here so aspect ratio does not distort speed
          panLeft((2 * deltaX * targetDistance) / element.clientHeight, scope.object.matrix)
          panUp((2 * deltaY * targetDistance) / element.clientHeight, scope.object.matrix)
        } else if (scope.object.isOrthographicCamera) {
          // orthographic
          panLeft((deltaX * (scope.object.right - scope.object.left)) / scope.object.zoom / element.clientWidth, scope.object.matrix)
          panUp((deltaY * (scope.object.top - scope.object.bottom)) / scope.object.zoom / element.clientHeight, scope.object.matrix)
        } else {
          // camera neither orthographic nor perspective
          console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.")
          scope.enablePan = false
        }
      }
    })()

    function dollyOut(dollyScale) {
      if (scope.object.isPerspectiveCamera || scope.object.isOrthographicCamera) {
        scale /= dollyScale
      } else {
        console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.")
        scope.enableZoom = false
      }
    }

    function dollyIn(dollyScale) {
      if (scope.object.isPerspectiveCamera || scope.object.isOrthographicCamera) {
        scale *= dollyScale
      } else {
        console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.")
        scope.enableZoom = false
      }
    }

    function updateZoomParameters(x, y) {
      if (!scope.zoomToCursor) {
        return
      }

      performCursorZoom = true

      const rect = scope.domElement.getBoundingClientRect()
      const dx = x
      const dy = y
      const w = rect.width
      const h = rect.height

      mouse.x = (dx / w) * 2 - 1
      mouse.y = -(dy / h) * 2 + 1

      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize()
    }

    function clampDistance(dist) {
      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist))
    }

    //
    // event callbacks - update the object state
    //

    function handleMouseDownRotate(event) {
      handleMouseDownDom()
      rotateStart.set(event.offsetX, event.offsetY)
    }

    function handleMouseDownDolly(event) {
      updateZoomParameters(event.offsetX, event.offsetX)
      dollyStart.set(event.offsetX, event.offsetY)

      // 设置target
      setTargetPositionFromTrackball()

      scope.update()
    }

    function handleMouseDownPan(event) {
      panStart.set(event.offsetX, event.offsetY)
    }

    function handleMouseDownDom() {
      _isMouseDown = true

      // const screenPosition = scope.target.clone().project(scope.object)
      // const offsetLeft = ((screenPosition.x + 1) / 2) * scope.domElement.offsetWidth + "px"
      // const offsetTop = (-(screenPosition.y - 1) / 2) * scope.domElement.offsetHeight + "px"
      // positionDom.style.left = offsetLeft
      // positionDom.style.top = offsetTop
    }

    function handleMouseMoveDom(offsetX, offsetY) {
      if (!_isMouseDown) {
        return
      }
      // scope.domElement.style.cursor = "none"
      // positionDom.style.display = "block"
      // rotationDom.style.display = "block"
      // rotationDom.style.left = offsetX + "px"
      // rotationDom.style.top = offsetY + "px"
    }

    function handleMouseUpDom() {
      // positionDom.style.display = "none"
      // rotationDom.style.display = "none"
      scope.domElement.style.cursor = "default"
    }

    function handleMouseMoveRotate(event) {
      rotateEnd.set(event.offsetX, event.offsetY)

      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)

      const element = scope.domElement

      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height

      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)

      rotateStart.copy(rotateEnd)

      scope.update()
    }

    function handleMouseMoveDolly(event) {
      dollyEnd.set(event.offsetX, event.offsetY)

      dollyDelta.subVectors(dollyEnd, dollyStart)

      if (dollyDelta.y > 0) {
        dollyOut(getZoomScale(dollyDelta.y))
      } else if (dollyDelta.y < 0) {
        dollyIn(getZoomScale(dollyDelta.y))
      }

      dollyStart.copy(dollyEnd)
      scope.domElement.style.cursor = "ns-resize"

      scope.update()
    }

    function handleMouseMovePan(event) {
      panEnd.set(event.offsetX, event.offsetY)

      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)

      pan(panDelta.x, panDelta.y)

      panStart.copy(panEnd)
      scope.domElement.style.cursor = "grab"

      scope.update()
    }

    function handleMouseWheel(event) {
      updateZoomParameters(event.offsetX, event.offsetY)

      if (event.deltaY < 0) {
        dollyIn(getZoomScale(event.deltaY))
      } else if (event.deltaY > 0) {
        dollyOut(getZoomScale(event.deltaY))
      }

      scope.update()
    }

    function handleKeyDown(event) {
      let needsUpdate = false

      switch (event.code) {
        case scope.keys.UP:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateUp((2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight)
          } else {
            pan(0, scope.keyPanSpeed)
          }

          needsUpdate = true
          break

        case scope.keys.BOTTOM:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateUp((-2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight)
          } else {
            pan(0, -scope.keyPanSpeed)
          }

          needsUpdate = true
          break

        case scope.keys.LEFT:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateLeft((2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight)
          } else {
            pan(scope.keyPanSpeed, 0)
          }

          needsUpdate = true
          break

        case scope.keys.RIGHT:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateLeft((-2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight)
          } else {
            pan(-scope.keyPanSpeed, 0)
          }

          needsUpdate = true
          break
      }

      if (needsUpdate) {
        // prevent the browser from scrolling on cursor keys
        // event.preventDefault();

        scope.update()
      }
    }

    function handleTouchStartRotate(event) {
      if (pointers.length === 1) {
        rotateStart.set(event.pageX, event.pageY)
      } else {
        const position = getSecondPointerPosition(event)

        const x = 0.5 * (event.pageX + position.x)
        const y = 0.5 * (event.pageY + position.y)

        rotateStart.set(x, y)
      }
    }

    function handleTouchStartPan(event) {
      if (pointers.length === 1) {
        panStart.set(event.pageX, event.pageY)
      } else {
        const position = getSecondPointerPosition(event)

        const x = 0.5 * (event.pageX + position.x)
        const y = 0.5 * (event.pageY + position.y)

        panStart.set(x, y)
      }
    }

    function handleTouchStartDolly(event) {
      const position = getSecondPointerPosition(event)

      const dx = event.pageX - position.x
      const dy = event.pageY - position.y

      const distance = Math.sqrt(dx * dx + dy * dy)

      dollyStart.set(0, distance)
    }

    function handleTouchStartDollyPan(event) {
      if (scope.enableZoom) handleTouchStartDolly(event)

      if (scope.enablePan) handleTouchStartPan(event)
    }

    function handleTouchStartDollyRotate(event) {
      if (scope.enableZoom) handleTouchStartDolly(event)

      if (scope.enableRotate) handleTouchStartRotate(event)
    }

    function handleTouchMoveRotate(event) {
      if (pointers.length == 1) {
        rotateEnd.set(event.pageX, event.pageY)
      } else {
        const position = getSecondPointerPosition(event)

        const x = 0.5 * (event.pageX + position.x)
        const y = 0.5 * (event.pageY + position.y)

        rotateEnd.set(x, y)
      }

      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)

      const element = scope.domElement

      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height

      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)

      rotateStart.copy(rotateEnd)
    }

    function handleTouchMovePan(event) {
      if (pointers.length === 1) {
        panEnd.set(event.pageX, event.pageY)
      } else {
        const position = getSecondPointerPosition(event)

        const x = 0.5 * (event.pageX + position.x)
        const y = 0.5 * (event.pageY + position.y)

        panEnd.set(x, y)
      }

      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)

      pan(panDelta.x, panDelta.y)

      panStart.copy(panEnd)
    }

    function handleTouchMoveDolly(event) {
      const position = getSecondPointerPosition(event)

      const dx = event.pageX - position.x
      const dy = event.pageY - position.y

      const distance = Math.sqrt(dx * dx + dy * dy)

      dollyEnd.set(0, distance)

      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed))

      dollyOut(dollyDelta.y)

      dollyStart.copy(dollyEnd)

      const centerX = (event.pageX + position.x) * 0.5
      const centerY = (event.pageY + position.y) * 0.5

      updateZoomParameters(centerX, centerY)
    }

    function handleTouchMoveDollyPan(event) {
      if (scope.enableZoom) handleTouchMoveDolly(event)

      if (scope.enablePan) handleTouchMovePan(event)
    }

    function handleTouchMoveDollyRotate(event) {
      if (scope.enableZoom) handleTouchMoveDolly(event)

      if (scope.enableRotate) handleTouchMoveRotate(event)
    }

    //
    // event handlers - FSM: listen for events and reset state
    //

    function onPointerDown(event) {
      if (scope.enabled === false) return
      // Prevent the browser from scrolling.
      // event.preventDefault();

      // 手动设置焦点，因为上面调用preventDefault会阻止浏览器自动设置焦点。
      // scope.domElement.focus ? scope.domElement.focus() : window.focus();

      if (pointers.length === 0) {
        scope.domElement.setPointerCapture(event.pointerId)

        scope.domElement.addEventListener("pointermove", onPointerMove)
        scope.domElement.addEventListener("pointerup", onPointerUp)
      }

      if (isTrackingPointer(event)) return

      addPointer(event)

      if (event.pointerType === "touch") {
        onTouchStart(event)
      } else {
        onMouseDown(event)
      }
    }

    function onPointerMove(event) {
      if (scope.enabled === false) return

      if (event.pointerType === "touch") {
        onTouchMove(event)
      } else {
        onMouseMove(event)
      }
    }

    function onPointerUp(event) {
      removePointer(event)

      switch (pointers.length) {
        case 0:
          scope.domElement.releasePointerCapture(event.pointerId)

          scope.domElement.removeEventListener("pointermove", onPointerMove)
          scope.domElement.removeEventListener("pointerup", onPointerUp)

          handleMouseUpDom()

          scope.dispatchEvent(_endEvent)

          state = STATE.NONE

          break

        case 1:
          const pointerId = pointers[0]
          const position = pointerPositions[pointerId]

          // minimal placeholder event - allows state correction on pointer-up
          onTouchStart({
            pointerId: pointerId,
            pageX: position.x,
            pageY: position.y,
          })

          break
      }
    }

    function onMouseDown(event) {
      let mouseAction

      switch (event.button) {
        case 0:
          mouseAction = scope.mouseButtons.LEFT
          break

        case 1:
          mouseAction = scope.mouseButtons.MIDDLE
          break

        case 2:
          mouseAction = scope.mouseButtons.RIGHT
          break

        default:
          mouseAction = -1
      }

      switch (mouseAction) {
        case THREE.MOUSE.DOLLY:
          if (scope.enableZoom === false) return

          handleMouseDownDolly(event)

          state = STATE.DOLLY

          break

        case THREE.MOUSE.ROTATE:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            if (scope.enablePan === false) return

            handleMouseDownPan(event)

            state = STATE.PAN
          } else {
            if (scope.enableRotate === false) return

            handleMouseDownRotate(event)

            state = STATE.ROTATE
          }

          break

        case THREE.MOUSE.PAN:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            if (scope.enableRotate === false) return

            handleMouseDownRotate(event)

            state = STATE.ROTATE
          } else {
            if (scope.enablePan === false) return

            handleMouseDownPan(event)

            state = STATE.PAN
          }

          break

        default:
          state = STATE.NONE
      }

      if (state !== STATE.NONE) {
        _startEvent.state = state
        scope.dispatchEvent(_startEvent)
      }
    }

    function onMouseMove(event) {
      switch (state) {
        case STATE.ROTATE:
          if (scope.enableRotate === false) return
          // handleMouseMoveRotate(event);
          handleMouseMoveDom(event.offsetX, event.offsetY)

          break

        case STATE.DOLLY:
          if (scope.enableZoom === false) return
          handleMouseMoveDolly(event)

          break

        case STATE.PAN:
          if (scope.enablePan === false) return
          handleMouseMovePan(event)

          break
      }
    }

    function onMouseWheel(event) {
      if (scope.enabled === false || scope.enableZoom === false || state !== STATE.NONE) return

      event.preventDefault()

      // 手动设置焦点，因为上面调用preventDefault会阻止浏览器自动设置焦点。
      scope.domElement.focus ? scope.domElement.focus() : window.focus()

      _startEvent.state = STATE.DOLLY
      scope.dispatchEvent(_startEvent)

      // 设置target
      setTargetPositionFromTrackball()

      handleMouseWheel(customWheelEvent(event))

      _endEvent.state = STATE.DOLLY
      scope.dispatchEvent(_endEvent)
    }

    function setTargetPositionFromTrackball() {
      scope.target.setFromMatrixPosition(scope.trackballToObject)
      const startDistance = scope.target.length()
      scope.target.set(0, 0, -startDistance)
      scope.target.applyQuaternion(scope.object.quaternion)
      scope.target.add(scope.object.position)

      scope.movedTarget()
    }

    function customWheelEvent(event) {
      const mode = event.deltaMode

      // minimal wheel event altered to meet delta-zoom demand
      const newEvent = {
        offsetX: event.offsetX,
        offsetY: event.offsetY,
        deltaY: event.deltaY,
      }

      switch (mode) {
        case 1: // LINE_MODE
          newEvent.deltaY *= 16
          break

        case 2: // PAGE_MODE
          newEvent.deltaY *= 100
          break
      }

      // detect if event was triggered by pinching
      if (event.ctrlKey && !controlActive) {
        newEvent.deltaY *= 10
      }

      return newEvent
    }

    function interceptControlDown(event) {
      if (event.key === "Control") {
        controlActive = true

        const document = scope.domElement.getRootNode() // offscreen canvas compatibility

        document.addEventListener("keyup", interceptControlUp, {
          passive: true,
          capture: true,
        })
      }
    }

    function interceptControlUp(event) {
      if (event.key === "Control") {
        controlActive = false

        const document = scope.domElement.getRootNode() // offscreen canvas compatibility

        document.removeEventListener("keyup", interceptControlUp, {
          passive: true,
          capture: true,
        })
      }
    }

    function onKeyDown(event) {
      if (scope.enabled === false || scope.enablePan === false) return

      handleKeyDown(event)
    }

    function onTouchStart(event) {
      trackPointer(event)

      switch (pointers.length) {
        case 1:
          switch (scope.touches.ONE) {
            case THREE.TOUCH.ROTATE:
              if (scope.enableRotate === false) return

              handleTouchStartRotate(event)

              state = STATE.TOUCH_ROTATE

              break

            case THREE.TOUCH.PAN:
              if (scope.enablePan === false) return

              handleTouchStartPan(event)

              state = STATE.TOUCH_PAN

              break

            default:
              state = STATE.NONE
          }

          break

        case 2:
          switch (scope.touches.TWO) {
            case THREE.TOUCH.DOLLY_PAN:
              if (scope.enableZoom === false && scope.enablePan === false) return

              handleTouchStartDollyPan(event)

              state = STATE.TOUCH_DOLLY_PAN

              break

            case THREE.TOUCH.DOLLY_ROTATE:
              if (scope.enableZoom === false && scope.enableRotate === false) return

              handleTouchStartDollyRotate(event)

              state = STATE.TOUCH_DOLLY_ROTATE

              break

            default:
              state = STATE.NONE
          }

          break

        default:
          state = STATE.NONE
      }

      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent)
      }
    }

    function onTouchMove(event) {
      trackPointer(event)

      switch (state) {
        case STATE.TOUCH_ROTATE:
          if (scope.enableRotate === false) return

          handleTouchMoveRotate(event)

          scope.update()

          break

        case STATE.TOUCH_PAN:
          if (scope.enablePan === false) return

          handleTouchMovePan(event)

          scope.update()

          break

        case STATE.TOUCH_DOLLY_PAN:
          if (scope.enableZoom === false && scope.enablePan === false) return

          handleTouchMoveDollyPan(event)

          scope.update()

          break

        case STATE.TOUCH_DOLLY_ROTATE:
          if (scope.enableZoom === false && scope.enableRotate === false) return

          handleTouchMoveDollyRotate(event)

          scope.update()

          break

        default:
          state = STATE.NONE
      }
    }

    function onContextMenu(event) {
      if (scope.enabled === false) return

      event.preventDefault()
    }

    function addPointer(event) {
      pointers.push(event.pointerId)
    }

    function removePointer(event) {
      delete pointerPositions[event.pointerId]

      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) {
          pointers.splice(i, 1)
          return
        }
      }
    }

    function isTrackingPointer(event) {
      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) return true
      }

      return false
    }

    function trackPointer(event) {
      let position = pointerPositions[event.pointerId]

      if (position === undefined) {
        position = new THREE.Vector2()
        pointerPositions[event.pointerId] = position
      }

      position.set(event.pageX, event.pageY)
    }

    function getSecondPointerPosition(event) {
      const pointerId = event.pointerId === pointers[0] ? pointers[1] : pointers[0]

      return pointerPositions[pointerId]
    }

    let _isMouseDown = false

    // 添加交互图标
    // const positionDom = document.createElement("div")
    // positionDom.style.width = "24px"
    // positionDom.style.height = "24px"
    // positionDom.style.backgroundImage = `url(${locationUrl})`
    // positionDom.style.backgroundSize = "contain"
    // positionDom.style.position = "absolute"
    // positionDom.style.display = "none"
    // positionDom.style.transform = "translate(-50%, -50%)"
    // scope.domElement.parentElement.appendChild(positionDom)

    // const rotationDom = document.createElement("div")
    // rotationDom.style.width = "24px"
    // rotationDom.style.height = "24px"
    // rotationDom.style.backgroundImage = `url(${rotationUrl})`
    // rotationDom.style.backgroundSize = "contain"
    // rotationDom.style.position = "absolute"
    // rotationDom.style.display = "none"
    // rotationDom.style.transform = "translate(-50%, -50%)"
    // scope.domElement.parentElement.appendChild(rotationDom)

    // // 添加事件
    scope.domElement.addEventListener("contextmenu", onContextMenu, false)
    scope.domElement.addEventListener("pointerdown", onPointerDown, false)
    scope.domElement.addEventListener("pointercancel", onPointerUp, false)
    scope.domElement.addEventListener("wheel", onMouseWheel, {
      passive: false,
    })

    // const document = scope.domElement.getRootNode(); // offscreen canvas compatibility

    document.addEventListener("keydown", interceptControlDown, true)

    scope.spinControl = new SpinControls(this.targetObj, 1, this.object, this.domElement)
    scope.spinControl.rotateSensitivity *= -1 // 将其设为负值，使相机在球体周围移动，就像球体是固定的一样

    // 如果是正交相机则需要改变交点投影模式
    if (scope.object.isOrthographicCamera) {
      scope.spinControl.setPointerToSphereMapping(scope.spinControl.POINTER_SPHERE_MAPPING.HOLROYD)
    }

    scope.spinControl.addEventListener("change", function (event) {
      scope.dispatchEvent(_changeEvent)
    })

    // Starts touch control off right
    this.adjustTrackballRadius()

    // force an update at start
    this.update()
  }
}

export { OrbitControls }
