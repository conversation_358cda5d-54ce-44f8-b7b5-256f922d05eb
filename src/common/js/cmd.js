import { genEnum } from "./util"

const CMD = {
  ...genEnum(
    [
      "TC_VtoM_NewProject",
      "TC_VtoM_LoadProject",
      "TC_VtoM_CloseProject",
      "TC_VtoM_ImportProject",
      "TC_VtoM_ImportMeasureData",
      "TC_VtoM_NewTank",
      "TC_VtoM_AddTank",
      "TC_VtoM_DeleteTank",
      "TC_VtoM_LoadTankInfo",
      "TC_VtoM_UpdateTankInfo",
      "TC_VtoM_LoadPointCloud",
      "TC_VtoM_UpdatePointCloud",
      "TC_VtoM_LoadPatchPara",
      "TC_VtoM_UpdatePatchPara",
      "TC_VtoM_LoadSectionPos",
      "TC_VtoM_UpdateSectionPos",
      "TC_VtoM_LoadBoundaryPos",
      "TC_VtoM_LoadSectionBoundary",
      "TC_VtoM_UpdateSectionBoundary",
      "TC_VtoM_LoadSectionPointCloud",
      "TC_VtoM_LoadTankBoundary",
      "TC_VtoM_LoadMeasureData",
      "TC_VtoM_LoadMainTank",
      "TC_VtoM_UpdateMainTank",
      "TC_VtoM_LoadCalculatePara",
      "TC_VtoM_UpdateCalculatePara",
      "TC_VtoM_Export",
      "TC_VtoM_LoadRecords",
      "TC_VtoM_LoadRecordTankList",
      "TC_VtoM_Save",
      "TC_VtoM_SelectDir",
      "TC_VtoM_LoadTankList",
      "TC_VtoM_CalTankPatch",
      "TC_VtoM_CalTankPosAvg",
      "TC_VtoM_CalTankBoundary",
      "TC_VtoM_CalSectionPosAvg",
      "TC_VtoM_CalBendPos",
      "TC_VtoM_CalCapacity",
      "TC_VtoM_LoadFile",
      "TC_VtoM_Permission",
      "TC_VtoM_CloseTank",

      "TC_VtoM_AddSectionPos",
      "TC_VtoM_DeleteSectionPos",
      "TC_VtoM_AddBoundaryPos",
      "TC_VtoM_DeleteBoundaryPos",
    ],
    0,
  ),
  ...genEnum(["TC_MtoV_WaitStart", "TC_MtoV_WaitEnd", "TC_MtoV_WaitPercent"], 10001),
}
export default CMD
