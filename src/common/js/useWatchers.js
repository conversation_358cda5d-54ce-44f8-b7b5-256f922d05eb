import { watch, ref } from "vue"
import { createDebouncedWatcher } from "./dataUtils"

export const useParameterWatchers = (options) => {
  const { watchData, updateCallback, debounceDelay = 500, loadData, getData } = options

  // 创建上次提交数据的响应式引用
  const lastSubmittedData = ref()

  watch(watchData, createDebouncedWatcher(updateCallback, debounceDelay), {
    deep: true,
  })

  loadData()

  return {
    lastSubmittedData,
  }
}
