/**
 * 通用工具函数
 * 提供常用的数据处理和比较功能
 */

/**
 * 防抖函数
 * @param {Function} fn - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} - 防抖后的函数
 */
export const debounce = (fn, delay = 500) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

/**
 * 比较两个对象是否相等
 * @param {Object} obj1 - 对象1
 * @param {Object} obj2 - 对象2
 * @returns {boolean} - 是否相等
 */
export const isEqual = (obj1, obj2) => {
  // 如果有一个为null，直接比较
  if (obj1 === null || obj2 === null) {
    return obj1 === obj2
  }

  // 转为JSON字符串比较
  return JSON.stringify(obj1) === JSON.stringify(obj2)
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} - 拷贝后的对象
 */
export const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj))
}

/**
 * 创建防抖监听器
 * @param {Function} callback - 回调函数
 * @param {number} delay - 防抖延迟时间
 * @returns {Function} - 防抖后的回调函数
 */
export const createDebouncedWatcher = (callback, delay = 500) => {
  return debounce(callback, delay)
}

export const isEmpty = (value) => {
  return value === "" || value === undefined || value === null
}

export const validatePosition = (value) => {
  if (typeof value !== "string") {
    return false
  }
  // 匹配格式：数字,数字（可以有小数点和负号）
  const regex = /^-?\d+(\.\d+)?,-?\d+(\.\d+)?$/
  return regex.test(value.trim())
}

/**
 * 格式化数字到指定小数位数
 * @param {number} num - 要格式化的数字
 * @param {number} decimals - 保留的小数位数，默认为1
 * @param {boolean} round - 是否四舍五入，默认为false（截断）
 * @returns {number} - 格式化后的数字
 */
export const formatNumber = (num, decimals = 1, round = true) => {
  const number = Number(num)
  if (isNaN(number)) {
    return num
  }

  const multiplier = Math.pow(10, decimals)

  if (round) {
    // 四舍五入
    return Math.round(number * multiplier) / multiplier
  } else {
    // 截断
    return Math.floor(number * multiplier) / multiplier
  }
}
