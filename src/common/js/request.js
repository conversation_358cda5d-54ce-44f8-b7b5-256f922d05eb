import CMD from "@/common/js/cmd"
import mockApi from "@/mock"
import Event from "@/common/js/event"

async function mock(name, data) {
  console.log("前端请求：", name, data)
  const res = await mockApi[name](data)
  const code = res.code.split("_")[0]
  console.log("接口返回：", name, res)
  if (code == 0) {
    return Promise.resolve(res.data)
  }
  return Promise.reject(res)
}

// 请求队列管理
class RequestQueue {
  constructor() {
    this.queue = []
    this.isProcessing = false
  }

  async add(requestFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ requestFn, resolve, reject })
      this.process()
    })
  }

  async process() {
    if (this.isProcessing || this.queue.length === 0) {
      return
    }

    this.isProcessing = true
    const { requestFn, resolve, reject } = this.queue.shift()

    try {
      const result = await requestFn()
      resolve(result)
    } catch (error) {
      reject(error)
    } finally {
      this.isProcessing = false
      // 处理下一个请求
      this.process()
    }
  }
}

// 创建全局请求队列实例
const requestQueue = new RequestQueue()

async function fetch(name, data) {
  const fn = CMD[name]

  if (name === "loadPath") {
    return requestQueue.add(() => {
      return new Promise((resolve, reject) => {
        window.Channel.loadPath(data).then(resolve, reject)
      })
    })
  }

  // 将请求包装成一个函数，加入队列
  return requestQueue.add(() => {
    return new Promise((resolve, reject) => {
      console.log("前端请求：", name, data)
      window.Channel.call(CMD[name], data)
      Event.once(fn, (res) => {
        const code = res.code.split("_")[0]
        console.log(`前端请求返回->`, res)
        if (code == 0) {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
    })
  })
}
export const request = import.meta.env.DEV ? mock : fetch
