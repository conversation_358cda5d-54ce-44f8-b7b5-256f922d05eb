/**
 * PCD (Point Cloud Data) 文件格式解析器
 * 专门用于解析PCD格式的字符串数据，提取点云信息
 *
 * 支持功能：
 * - ASCII和Binary两种PCD格式解析
 * - 常见字段类型：x, y, z, rgb, rgba, r, g, b, normal_x, normal_y, normal_z
 * - 自动计算边界框和统计信息
 * - Three.js兼容的数据格式输出
 * - 完善的错误处理和验证
 */

/**
 * 解析PCD文件头信息
 * @param {string} headerSection - PCD文件头部分的字符串
 * @returns {Object} - 解析后的头信息对象
 */
const parsePCDHeader = (headerSection) => {
  const lines = headerSection.split("\n").filter((line) => line.trim())
  const header = {
    version: "0.7",
    fields: [],
    size: [],
    type: [],
    count: [],
    width: 0,
    height: 0,
    viewpoint: [0, 0, 0, 1, 0, 0, 0],
    points: 0,
    data: "ascii",
  }

  for (const line of lines) {
    const trimmedLine = line.trim()
    if (!trimmedLine || trimmedLine.startsWith("#")) continue

    const parts = trimmedLine.split(/\s+/)
    const keyword = parts[0].toUpperCase()

    switch (keyword) {
      case "VERSION":
        header.version = parts[1]
        break
      case "FIELDS":
        header.fields = parts.slice(1)
        break
      case "SIZE":
        header.size = parts.slice(1).map(Number)
        break
      case "TYPE":
        header.type = parts.slice(1)
        break
      case "COUNT":
        header.count = parts.slice(1).map(Number)
        break
      case "WIDTH":
        header.width = parseInt(parts[1])
        break
      case "HEIGHT":
        header.height = parseInt(parts[1])
        break
      case "VIEWPOINT":
        header.viewpoint = parts.slice(1).map(Number)
        break
      case "POINTS":
        header.points = parseInt(parts[1])
        break
      case "DATA":
        header.data = parts[1].toLowerCase()
        break
    }
  }

  return header
}

/**
 * 解析ASCII格式的点云数据
 * @param {string} dataSection - 数据部分的字符串
 * @param {Object} header - 头信息对象
 * @returns {Object} - 包含解析后的点云数据
 */
const parseASCIIData = (dataSection, header) => {
  const lines = dataSection.split("\n").filter((line) => line.trim())
  const points = []
  const colors = []
  const normals = []

  // 创建字段索引映射
  const fieldIndices = {}
  header.fields.forEach((field, index) => {
    fieldIndices[field.toLowerCase()] = index
  })

  const hasColor = header.fields.some((field) => ["rgb", "rgba", "r", "g", "b"].includes(field.toLowerCase()))
  const hasNormal = header.fields.some((field) => ["normal_x", "normal_y", "normal_z"].includes(field.toLowerCase()))

  for (const line of lines) {
    const values = line.trim().split(/\s+/).map(Number)
    if (values.length < header.fields.length) continue

    // 提取坐标 (x, y, z)
    const x = values[fieldIndices.x] || 0
    const y = values[fieldIndices.y] || 0
    const z = values[fieldIndices.z] || 0
    points.push(x, y, z)

    // 提取颜色信息
    if (hasColor) {
      let r = 255,
        g = 255,
        b = 255

      if (fieldIndices.rgb !== undefined) {
        // RGB打包格式
        const rgb = values[fieldIndices.rgb]
        r = (rgb >> 16) & 0xff
        g = (rgb >> 8) & 0xff
        b = rgb & 0xff
      } else if (fieldIndices.rgba !== undefined) {
        // RGBA打包格式
        const rgba = values[fieldIndices.rgba]
        r = (rgba >> 24) & 0xff
        g = (rgba >> 16) & 0xff
        b = (rgba >> 8) & 0xff
      } else {
        // 分离的RGB通道
        r = fieldIndices.r !== undefined ? values[fieldIndices.r] : 255
        g = fieldIndices.g !== undefined ? values[fieldIndices.g] : 255
        b = fieldIndices.b !== undefined ? values[fieldIndices.b] : 255
      }

      colors.push(r / 255, g / 255, b / 255)
    }

    // 提取法向量信息
    if (hasNormal) {
      const nx = values[fieldIndices.normal_x] || 0
      const ny = values[fieldIndices.normal_y] || 0
      const nz = values[fieldIndices.normal_z] || 0
      normals.push(nx, ny, nz)
    }
  }

  return {
    positions: new Float32Array(points),
    colors: colors.length > 0 ? new Float32Array(colors) : null,
    normals: normals.length > 0 ? new Float32Array(normals) : null,
    pointCount: points.length / 3,
  }
}

/**
 * 解析二进制格式的点云数据
 * @param {ArrayBuffer} dataBuffer - 二进制数据缓冲区
 * @param {Object} header - 头信息对象
 * @returns {Object} - 包含解析后的点云数据
 */
const parseBinaryData = (dataBuffer, header) => {
  const view = new DataView(dataBuffer)
  const points = []
  const colors = []
  const normals = []

  // 计算每个点的字节大小
  const pointSize = header.size.reduce((sum, size) => sum + size, 0)
  const pointCount = header.points

  // 创建字段偏移映射
  const fieldOffsets = {}
  let offset = 0
  header.fields.forEach((field, index) => {
    fieldOffsets[field.toLowerCase()] = offset
    offset += header.size[index]
  })

  const hasColor = header.fields.some((field) => ["rgb", "rgba", "r", "g", "b"].includes(field.toLowerCase()))
  const hasNormal = header.fields.some((field) => ["normal_x", "normal_y", "normal_z"].includes(field.toLowerCase()))

  for (let i = 0; i < pointCount; i++) {
    const pointOffset = i * pointSize

    // 提取坐标 (x, y, z)
    const x = view.getFloat32(pointOffset + fieldOffsets.x, true)
    const y = view.getFloat32(pointOffset + fieldOffsets.y, true)
    const z = view.getFloat32(pointOffset + fieldOffsets.z, true)
    points.push(x, y, z)

    // 提取颜色信息
    if (hasColor) {
      let r = 255,
        g = 255,
        b = 255

      if (fieldOffsets.rgb !== undefined) {
        const rgb = view.getUint32(pointOffset + fieldOffsets.rgb, true)
        r = (rgb >> 16) & 0xff
        g = (rgb >> 8) & 0xff
        b = rgb & 0xff
      } else if (fieldOffsets.rgba !== undefined) {
        const rgba = view.getUint32(pointOffset + fieldOffsets.rgba, true)
        r = (rgba >> 24) & 0xff
        g = (rgba >> 16) & 0xff
        b = (rgba >> 8) & 0xff
      } else {
        r = fieldOffsets.r !== undefined ? view.getUint8(pointOffset + fieldOffsets.r) : 255
        g = fieldOffsets.g !== undefined ? view.getUint8(pointOffset + fieldOffsets.g) : 255
        b = fieldOffsets.b !== undefined ? view.getUint8(pointOffset + fieldOffsets.b) : 255
      }

      colors.push(r / 255, g / 255, b / 255)
    }

    // 提取法向量信息
    if (hasNormal) {
      const nx = view.getFloat32(pointOffset + fieldOffsets.normal_x, true)
      const ny = view.getFloat32(pointOffset + fieldOffsets.normal_y, true)
      const nz = view.getFloat32(pointOffset + fieldOffsets.normal_z, true)
      normals.push(nx, ny, nz)
    }
  }

  return {
    positions: new Float32Array(points),
    colors: colors.length > 0 ? new Float32Array(colors) : null,
    normals: normals.length > 0 ? new Float32Array(normals) : null,
    pointCount: points.length / 3,
  }
}

/**
 * 计算点云数据的边界框
 * @param {Float32Array} positions - 点位置数据
 * @returns {Object} - 边界框信息
 */
const calculateBoundingBox = (positions) => {
  let minX = Infinity,
    minY = Infinity,
    minZ = Infinity
  let maxX = -Infinity,
    maxY = -Infinity,
    maxZ = -Infinity

  for (let i = 0; i < positions.length; i += 3) {
    const x = positions[i]
    const y = positions[i + 1]
    const z = positions[i + 2]

    minX = Math.min(minX, x)
    maxX = Math.max(maxX, x)
    minY = Math.min(minY, y)
    maxY = Math.max(maxY, y)
    minZ = Math.min(minZ, z)
    maxZ = Math.max(maxZ, z)
  }

  return {
    min: { x: minX, y: minY, z: minZ },
    max: { x: maxX, y: maxY, z: maxZ },
    size: {
      x: maxX - minX,
      y: maxY - minY,
      z: maxZ - minZ,
    },
  }
}

/**
 * 主要的PCD字符串解析函数
 * @param {string|ArrayBuffer} pcdData - PCD格式的字符串或二进制数据
 * @returns {Object} - 解析结果对象
 */
export const parsePCDString = (pcdData) => {
  try {
    let header, pointCloudData

    if (typeof pcdData === "string") {
      // ASCII格式解析
      const sections = pcdData.split("DATA")
      if (sections.length !== 2) {
        throw new Error("无效的PCD格式：缺少DATA标识符")
      }

      const headerSection = sections[0].trim()
      const dataSection = sections[1].trim()

      header = parsePCDHeader(headerSection)

      if (header.data === "ascii") {
        pointCloudData = parseASCIIData(dataSection, header)
      } else {
        throw new Error("字符串格式只支持ASCII数据类型")
      }
    } else if (pcdData instanceof ArrayBuffer) {
      // 二进制格式解析
      const decoder = new TextDecoder("utf-8")
      const text = decoder.decode(pcdData.slice(0, 2048)) // 读取前2KB来查找头信息

      const dataIndex = text.indexOf("DATA")
      if (dataIndex === -1) {
        throw new Error("无效的PCD格式：缺少DATA标识符")
      }

      const headerSection = text.substring(0, dataIndex).trim()
      header = parsePCDHeader(headerSection)

      // 找到数据开始位置
      const headerBytes = new TextEncoder().encode(headerSection + "\nDATA ").length
      const dataBuffer = pcdData.slice(headerBytes)

      if (header.data === "binary") {
        pointCloudData = parseBinaryData(dataBuffer, header)
      } else {
        throw new Error("ArrayBuffer格式只支持binary数据类型")
      }
    } else {
      throw new Error("不支持的数据类型，请传入字符串或ArrayBuffer")
    }

    // 计算边界框
    const boundingBox = calculateBoundingBox(pointCloudData.positions)

    return {
      success: true,
      header,
      data: pointCloudData,
      boundingBox,
      statistics: {
        pointCount: pointCloudData.pointCount,
        hasColors: pointCloudData.colors !== null,
        hasNormals: pointCloudData.normals !== null,
        fields: header.fields,
        dataType: header.data,
      },
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: null,
    }
  }
}

/**
 * 验证PCD字符串格式
 * @param {string} pcdString - PCD格式字符串
 * @returns {Object} - 验证结果
 */
export const validatePCDString = (pcdString) => {
  try {
    if (typeof pcdString !== "string") {
      return { valid: false, error: "输入必须是字符串类型" }
    }

    if (!pcdString.includes("DATA")) {
      return { valid: false, error: "缺少DATA标识符" }
    }

    const lines = pcdString.split("\n")
    const hasVersion = lines.some((line) => line.trim().toUpperCase().startsWith("VERSION"))
    const hasFields = lines.some((line) => line.trim().toUpperCase().startsWith("FIELDS"))
    const hasPoints = lines.some((line) => line.trim().toUpperCase().startsWith("POINTS"))

    if (!hasVersion) {
      return { valid: false, error: "缺少VERSION字段" }
    }
    if (!hasFields) {
      return { valid: false, error: "缺少FIELDS字段" }
    }
    if (!hasPoints) {
      return { valid: false, error: "缺少POINTS字段" }
    }

    return { valid: true, error: null }
  } catch (error) {
    return { valid: false, error: error.message }
  }
}

/**
 * 将解析后的点云数据转换为Three.js可用的格式
 * @param {Object} parsedData - 解析后的PCD数据
 * @returns {Object} - Three.js格式的几何体数据
 */
export const convertToThreeJSGeometry = (parsedData) => {
  if (!parsedData.success) {
    throw new Error(`PCD解析失败: ${parsedData.error}`)
  }

  const { data } = parsedData

  return {
    positions: data.positions,
    colors: data.colors,
    normals: data.normals,
    pointCount: data.pointCount,
  }
}

/**
 * 创建Three.js BufferGeometry对象
 * @param {Object} parsedData - 解析后的PCD数据
 * @returns {THREE.BufferGeometry} - Three.js几何体对象
 */
export const createThreeJSGeometry = (parsedData) => {
  if (typeof window === "undefined" || !window.THREE) {
    throw new Error("Three.js未加载，无法创建BufferGeometry")
  }

  const geometryData = convertToThreeJSGeometry(parsedData)
  const geometry = new THREE.BufferGeometry()

  // 设置位置属性
  geometry.setAttribute("position", new THREE.BufferAttribute(geometryData.positions, 3))

  // 设置颜色属性
  if (geometryData.colors) {
    geometry.setAttribute("color", new THREE.BufferAttribute(geometryData.colors, 3))
  }

  // 设置法向量属性
  if (geometryData.normals) {
    geometry.setAttribute("normal", new THREE.BufferAttribute(geometryData.normals, 3))
  }

  return geometry
}

/**
 * 获取PCD文件的元数据信息
 * @param {string} pcdString - PCD格式字符串
 * @returns {Object} - 元数据信息
 */
export const getPCDMetadata = (pcdString) => {
  try {
    const sections = pcdString.split("DATA")
    if (sections.length !== 2) {
      throw new Error("无效的PCD格式")
    }

    const header = parsePCDHeader(sections[0].trim())

    return {
      success: true,
      metadata: {
        version: header.version,
        fields: header.fields,
        fieldTypes: header.type,
        fieldSizes: header.size,
        fieldCounts: header.count,
        width: header.width,
        height: header.height,
        points: header.points,
        dataType: header.data,
        viewpoint: header.viewpoint,
      },
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
    }
  }
}

/**
 * 生成示例PCD数据
 * @param {number} pointCount - 点的数量
 * @param {boolean} includeColors - 是否包含颜色
 * @returns {string} - 示例PCD字符串
 */
export const generateSamplePCD = (pointCount = 8, includeColors = true) => {
  const fields = includeColors ? "x y z rgb" : "x y z"
  const size = includeColors ? "4 4 4 4" : "4 4 4"
  const type = includeColors ? "F F F U" : "F F F"
  const count = includeColors ? "1 1 1 1" : "1 1 1"

  let header = `# .PCD v0.7 - Point Cloud library
VERSION 0.7
FIELDS ${fields}
SIZE ${size}
TYPE ${type}
COUNT ${count}
WIDTH ${pointCount}
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS ${pointCount}
DATA ascii`

  let dataLines = []
  for (let i = 0; i < pointCount; i++) {
    const x = Math.random() * 2 - 1
    const y = Math.random() * 2 - 1
    const z = Math.random() * 2 - 1

    if (includeColors) {
      const r = Math.floor(Math.random() * 256)
      const g = Math.floor(Math.random() * 256)
      const b = Math.floor(Math.random() * 256)
      const rgb = (r << 16) | (g << 8) | b
      dataLines.push(`${x.toFixed(5)} ${y.toFixed(5)} ${z.toFixed(5)} ${rgb}`)
    } else {
      dataLines.push(`${x.toFixed(5)} ${y.toFixed(5)} ${z.toFixed(5)}`)
    }
  }

  return header + "\n" + dataLines.join("\n")
}

// 默认导出主要功能
export default {
  parsePCDString,
  validatePCDString,
  convertToThreeJSGeometry,
  createThreeJSGeometry,
  getPCDMetadata,
  generateSamplePCD,
}
