import CMD from "./cmd"

class Channel {
  messageHandlerName = "MessageHandler"
  constructor() {
    this.channel = null
    this.isReady = false

    if (!window.QWebChannel || !window.qt) {
      return
    }
    new QWebChannel(qt.webChannelTransport, (channel) => {
      this.channel = channel

      this.watch()

      this.isReady = true
      globalEvent.emit("ready")
    })
  }
  ready() {
    if (this.isReady) {
      return Promise.resolve()
    }
    return new Promise((resolve) => {
      globalEvent.on("ready", resolve)
    })
  }

  async loadPath(path) {
    const cmd = CMD["TC_VtoM_LoadFile"]
    this.call(cmd, path)
    return new Promise((resolve, reject) => {
      let chunk = []
      globalEvent.on(cmd, (res) => {
        console.log(`output->cmd`, res)
        if (res.code === "4_38") {
          //开始
        } else if (res.code === "6_38") {
          //传输
          chunk.push(res.data.value)
        } else if (res.code === "5_38") {
          console.log(`output->chunk`, chunk)
          //完成
          resolve(chunk.join(""))
        } else {
          reject()
        }
      })
    })
  }
  watch() {
    const objects = this.channel.objects[this.messageHandlerName]

    objects.qt2web.connect((cmd, data) => {
      // console.log(`数据返回`, cmd, data)

      let res

      if (data) {
        res = JSON.parse(data)
      }

      if ([CMD["TC_MtoV_WaitStart"], CMD["TC_MtoV_WaitEnd"], CMD["TC_MtoV_WaitPercent"]].includes(cmd)) {
        globalEvent.emit(cmd, res)
        return
      }

      globalEvent.emit(this.fn, res)

      // this.fn = undefined
    })
  }

  call(fn, data) {
    this.fn = fn

    this.channel.objects[this.messageHandlerName].web2qt(fn, JSON.stringify(data))
  }
}
window.Channel = new Channel()
