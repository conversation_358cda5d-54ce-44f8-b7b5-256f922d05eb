/* ================== Element Plus 主题定制 ================== */
:root {
  --el-color-primary: #2680eb;
  --el-color-primary-light-3: #4a94ee;
  --el-color-primary-light-5: #6ea8f1;
  --el-color-primary-light-7: #92bcf4;
  --el-color-primary-light-9: #b6d0f7;
  --el-color-primary-dark-2: #1b6ad4;

  /* 表格相关变量 */
  --table-cell-height: 24px;
  --table-header-bg-color: #c5c5c5;
  --table-row-active-bg: #f6ffed;
  --table-row-hover-bg: #f6ffed;
}

/* ================== 对话框样式 ================== */
.el-dialog {
  --el-border-radius-base: 12px;
}

/* ================== 表格基础样式 ================== */
.el-table {
  --el-table-header-bg-color: var(--table-header-bg-color);
}

/* 表格边框和单元格基础设置 */
.el-table--border tbody .el-table__cell {
  padding: 0;
  --cell-height: var(--table-cell-height);
  line-height: var(--cell-height);

  .cell {
    padding: 0;
    line-height: var(--cell-height);

    .custom-cell {
      padding: 0 8px;
    }

    /* 包含输入框或选择器的单元格 */
    &:has(.el-input),
    &:has(.el-select) {
      .custom-cell {
        padding: 0;
      }
    }
  }
}

/* 选择列特殊处理 */
.el-table__cell.el-table-column--selection {
  .cell {
    padding: 0 8px !important;
  }
}

/* ================== 表格行状态样式 ================== */
/* 当前选中行样式 */
.el-table__body tr.current-row > td.el-table__cell {
  background-color: #e6f4ff !important;
  color: #1890ff !important;
}

/* 行悬停样式 */
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  .el-input__wrapper {
    background-color: #fff;
  }
}

/* 活跃行样式（BaseTable组件使用） */
.base-table-wrapper {
  height: 100%;

  .active-row {
    background-color: var(--table-row-active-bg);
    --el-table-row-hover-bg-color: var(--table-row-hover-bg);

    .custom-cell:focus {
      background-color: var(--table-row-active-bg);
    }
  }
}

.table-area {
  height: 100%;
}

/* ================== 自定义单元格样式 ================== */
.custom-cell {
  height: var(--cell-height);
  --el-checkbox-height: var(--cell-height);
  &:focus {
    outline: none;
  }

  /* 表单控件通用样式 */
  .el-select,
  .el-input,
  .el-input-number {
    vertical-align: top;
    outline: none;
  }

  /* 选择器样式 */
  .el-select__wrapper {
    height: calc(var(--cell-height) - 4px);
    min-height: auto;
    --el-border-radius-base: 0;
    box-shadow: none;
    padding: 0 8px;

    .el-select__selected-item {
      font-size: 12px;
    }
  }

  /* 数字输入框样式 */
  .el-input-number {
    width: 100%;

    .el-input__inner {
      text-align: left;
    }
  }

  /* 输入框样式 */
  .el-input {
    font-size: 12px;
    --el-input-height: calc(var(--cell-height) - 4px);
    --el-input-inner-height: calc(var(--cell-height) - 4px);
    vertical-align: top;

    .el-input__wrapper {
      border-radius: 0;
      box-shadow: none;
      padding: 0 8px;
    }

    input {
      background-color: transparent;
    }
  }

  /* ================== 单元格状态样式 ================== */
  /* 选中/焦点状态 */
  &.selected-cell {
    background-color: #e6f7ff !important;
    // border: 2px solid #1890ff !important ;
    outline: none;
  }

  /* 编辑状态 - 最高优先级，覆盖所有其他状态 */
  &.active-cell {
    background-color: #fff !important;
    border: 2px solid #52c41a !important;
    outline: none;
  }
}

/* ================== 行选中状态下的单元格样式优先级 ================== */
.el-table__body tr.current-row > td.el-table__cell .custom-cell.active-cell {
  background-color: #409eff !important;
  color: #fff !important;
  border: 2px solid #1890ff;
  box-sizing: border-box;
}

.el-table__body tr.current-row > td.el-table__cell .custom-cell.selected-cell {
  background-color: #bfdbfe !important;
  color: #1e40af !important;
  border: 1px solid #409eff;
  box-sizing: border-box;
}

.el-table__body tr.current-row > td.el-table__cell .custom-cell:focus {
  background-color: #dbeafe !important;
  color: #1e40af !important;
  border: 1px solid #409eff;
  box-sizing: border-box;
}

/* 行高亮时单元格样式优化 */
.active-row {
  .custom-cell {
    /* 在行高亮时，选中单元格保持更明显的对比 */
    &.selected-cell {
      background-color: #e6f7ff !important;
    }
  }
}

/* ================== 右键菜单样式 ================== */
.context-menu {
  min-width: 180px;
  overflow: hidden;
  transition: all 0.2s ease;

  .menu-item {
    height: 32px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #606266;
    cursor: pointer;
    border-bottom: 1px solid rgba(235, 238, 245, 0.6);
    transition: all 0.15s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #ecf5ff;
      color: #409eff;
    }

    i {
      margin-right: 8px;
      font-size: 14px;
    }

    span {
      flex: 1;
    }
  }
}

/* ================== 其他Element Plus组件样式 ================== */
.el-tabs__item:focus-visible {
  box-shadow: none;
}
