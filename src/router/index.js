/**
 * 路由配置文件
 */

import { createRouter, createWebHashHistory } from "vue-router"
import Home from "@/views/home/<USER>"
import HomeIndex from "@/views/home/<USER>"
import Calc from "@/views/home/<USER>/index.vue"
import Log from "@/views/home/<USER>/index.vue"
import ProjectInfo from "@/views/workspace/projectInfo.vue"
import ModelImport from "@/views/workspace/modelImport/index.vue"
import Preprocessing from "@/views/workspace/preprocessing.vue"
import Section from "@/views/workspace/section/index.vue"
import ContourPoints from "@/views/workspace/contourPoints/index.vue"
import CabinCapacity from "@/views/workspace/cabinCapacity.vue"
import ComponentDefinition from "@/views/workspace/componentDefinition/index.vue"
import ProgressBarDemo from "@/components/ProgressBarDemo.vue"

import Workspace from "@/views/workspace/index.vue"

// 路由配置
const routes = [
  {
    path: "/",
    component: Home, // 首页
    children: [
      {
        path: "",
        component: HomeIndex,
      },
      {
        path: "clac",
        component: Calc,
      },
      {
        path: "log",
        component: Log,
      },
      {
        path: "progress-demo",
        component: ProgressBarDemo,
      },
    ],
  },

  {
    path: "/workspace",
    component: Workspace, // 工作空间页面
    redirect: "/workspace/projectInfo",
    children: [
      {
        path: "projectInfo",
        component: ProjectInfo, // 项目信息
      },
      {
        path: "modelImport",
        component: ModelImport, // 模型导入页面
      },
      {
        path: "preprocessing",
        component: Preprocessing, // 数据预处理页面
      },
      {
        path: "section",
        component: Section, // 剖面分析页面
      },
      {
        path: "contourPoints",
        component: ContourPoints, // 轮廓点提取页面
      },
      {
        path: "cabinCapacity",
        component: CabinCapacity, // 舱容计算页面
      },
      {
        path: "componentDefinition",
        component: ComponentDefinition, // 构件定义页面
      },
    ],
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

export default router
