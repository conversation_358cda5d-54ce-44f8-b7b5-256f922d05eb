<template>
  <router-view />
  <GlobalProgressBar />
</template>

<script setup>
import CMD from "@/common/js/cmd"
import GlobalProgressBar from "./components/GlobalProgressBar.vue"
import { progressBar } from "@/store/progress"
import { useStore } from "@/store"

const store = useStore()

window.Channel.ready().then(() => {
  store.loadPermission()
})

globalEvent.on(CMD["TC_MtoV_WaitStart"], () => {
  progressBar.show()
})

globalEvent.on(CMD["TC_MtoV_WaitPercent"], (res) => {
  progressBar.show()
  progressBar.setProgress(res.data.value)
})
globalEvent.on(CMD["TC_MtoV_WaitEnd"], () => {
  progressBar.hide()
})
</script>
