<template>
  <el-dialog v-model="dialogVisible" title="页面" width="800px" :close-on-click-modal="false">
    <div class="flex flex-col gap-4">
      <div class="flex items-center">
        <span class="w-16">位置</span>
        <el-input v-model.number="formData.position" type="number" />
        <el-checkbox v-model="formData.isBend" class="ml-4">折点</el-checkbox>
      </div>
      <div class="mt-4 h-400px">
        <BaseTable v-model:data="points" :columns="pointColumns" :can-add="true" :init-row="initRow" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue"
import { ElDialog, ElButton, ElInput, ElCheckbox, ElMessage } from "element-plus"
import BaseTable from "@/components/baseTable/index.vue"
import { EMPTY_ROW_SYMBOL } from "@/components/baseTable/constant"
import { deepClone, validatePosition, isEmpty } from "@/common/js/dataUtils"
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["update:visible", "confirm"])

const dialogVisible = defineModel("visible", { type: Boolean, default: false })
const formData = ref({
  position: "",
  isBend: false,
})

const points = ref([])

// 轮廓点表格配置
const pointColumns = [
  {
    label: "编号",
    render({ row, index }) {
      if (row.hasOwnProperty(EMPTY_ROW_SYMBOL)) {
        return
      }
      return h("div", { class: "custom-cell" }, index + 1)
    },
  },
  {
    label: "坐标",
    prop: "point",
    editor: {},
  },
  {
    label: "折点",
    prop: "bend",
    render({ row }) {
      if (row.hasOwnProperty(EMPTY_ROW_SYMBOL)) {
        return
      }
      return h(ElCheckbox, {
        class: "ml-2",
        modelValue: row.bend,
        "onUpdate:modelValue": (value) => {
          row.bend = value
        },
      })
    },
  },
]

// 监听visible属性变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val) {
      // 重置表单数据
      formData.value = {
        position: "",
        isBend: false,
      }
      points.value = []
    }
  },
)

// 监听弹框显示状态变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit("update:visible", val)
  },
)

const initRow = () => {
  return {
    point: undefined,
    bend: false,
  }
}

const handleConfirm = () => {
  if (isEmpty(formData.value.position)) {
    ElMessage.warning("请输入位置")
    return
  }

  const pointsData = deepClone(points.value).filter((item) => item.point !== undefined && validatePosition(item.point))

  if (pointsData.length === 0) {
    ElMessage.warning("请添加轮廓点")
    return
  }

  emit("confirm", {
    pos: formData.value.position,
    bend: formData.value.isBend,
    points: pointsData,
  })

  dialogVisible.value = false
}

const handleCancel = () => {
  dialogVisible.value = false
}
</script>
