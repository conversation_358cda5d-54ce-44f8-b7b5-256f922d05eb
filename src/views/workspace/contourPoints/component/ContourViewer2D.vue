<template>
  <div ref="containerRef" class="w-full h-full bg-gray-50 rounded-lg overflow-hidden border border-gray-200"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue"
import * as THREE from "three"
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js"
import { useStore } from "@/store/index"

const props = defineProps({
  pointIndex: {
    type: Number,
    default: -1,
  },
})

const emit = defineEmits(["update:pointIndex"])

const store = useStore()
const containerRef = ref(null)
let scene, camera, renderer, raycaster, mouse
let pointGroup, lineGroup
let controls
let pointsObject, lineObject, pointsGeometry, lineGeometry

// 颜色配置 - 突出连线，优化链接点颜色
const colors = {
  // 页面点颜色（点云数据）- 使用柔和的灰色，作为背景元素
  pagePoint: {
    normal: "#94a3b8", // 浅灰色，降低视觉权重
    stroke: "#64748b", // 中灰色边框
  },
  // 线点颜色（轮廓点）- 使用蓝色系，避免红色
  linePoint: {
    normal: "#3b82f6", // 蓝色 - 普通状态，清晰可见
    normalStroke: "#1d4ed8", // 深蓝色边框
    selected: "#f59e0b", // 黄色 - 选中状态，高对比度醒目
    selectedStroke: "#d97706", // 深黄色边框
    hover: "#8b5cf6", // 紫色（备用悬停状态）
  },
  // 连接线颜色 - 使用高对比度的深色，确保连线非常明显
  line: {
    normal: "#1f2937", // 深灰黑色 - 普通状态，非常明显
    selected: "#dc2626", // 红色 - 选中状态，与黄色点形成强对比
  },
  // 背景相关
  background: "#f8fafc", // 浅灰色背景
}

// 视图变换状态
const viewState = ref({
  scale: 1,
  offsetX: 0,
  offsetY: 0,
  // 移除minScale和maxScale，现在使用OrbitControls的缩放限制
})

// 鼠标交互状态
const mouseState = ref({
  isDragging: false,
  lastX: 0,
  lastY: 0,
})

// 存储事件监听器引用，用于清理
const eventListeners = ref({
  mousedown: null,
  mousemove: null,
  mouseup: null,
  mouseleave: null,
  wheel: null,
  click: null,
})

// 初始化Three.js场景
const initScene = () => {
  if (!containerRef.value) return

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(colors.background)

  // 创建相机（正交相机更适合2D视图）
  const width = containerRef.value.offsetWidth
  const height = containerRef.value.offsetHeight
  const aspect = width / height
  camera = new THREE.OrthographicCamera(
    -width / 2, // left
    width / 2, // right
    height / 2, // top
    -height / 2, // bottom
    0.1, // near
    1000, // far
  )
  camera.position.z = 10

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  containerRef.value.appendChild(renderer.domElement)

  // 创建射线投射器（用于点击检测）
  raycaster = new THREE.Raycaster()
  mouse = new THREE.Vector2()

  // 创建组
  pointGroup = new THREE.Group()
  lineGroup = new THREE.Group()
  scene.add(pointGroup)
  scene.add(lineGroup)

  // 初始化控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableRotate = false // 禁用旋转（保持2D视图）
  controls.enablePan = true
  controls.enableZoom = true
  controls.minZoom = 0.1
  controls.maxZoom = 10
  controls.mouseButtons = {
    LEFT: THREE.MOUSE.PAN,
    MIDDLE: THREE.MOUSE.DOLLY,
    RIGHT: THREE.MOUSE.ROTATE,
  }
  // 移除翻转设置，使用自定义事件处理器来控制平移
  controls.enabled = false // 禁用内置控制器
  controls.update()

  // 添加事件监听器
  addMouseEventListeners()

  // 开始渲染循环
  animate()
}

const animate = () => {
  requestAnimationFrame(animate)
  renderer.render(scene, camera)
}

const clearScene = () => {
  clearPoints()
  clearLines()
}

const clearPoints = () => {
  if (pointGroup) {
    while (pointGroup.children.length > 0) {
      const object = pointGroup.children[0]
      if (object.geometry) object.geometry.dispose()
      if (object.material) object.material.dispose()
      pointGroup.remove(object)
    }
  }
}

const clearLines = () => {
  if (lineGroup) {
    while (lineGroup.children.length > 0) {
      const object = lineGroup.children[0]
      if (object.geometry) object.geometry.dispose()
      if (object.material) object.material.dispose()
      lineGroup.remove(object)
    }
  }
}

// 添加鼠标事件监听器
const addMouseEventListeners = () => {
  if (!containerRef.value) return

  const container = containerRef.value

  // 鼠标按下事件
  eventListeners.value.mousedown = (e) => {
    mouseState.value.isDragging = true
    mouseState.value.lastX = e.clientX
    mouseState.value.lastY = e.clientY
    container.style.cursor = "grabbing"
  }
  container.addEventListener("mousedown", eventListeners.value.mousedown)

  // 鼠标移动事件
  eventListeners.value.mousemove = (e) => {
    if (!mouseState.value.isDragging) return

    const deltaX = e.clientX - mouseState.value.lastX
    const deltaY = e.clientY - mouseState.value.lastY

    // 根据相机的缩放比例调整平移速度
    const panSpeed = 1 / camera.zoom

    // 更新相机位置，实现平移效果
    camera.position.x -= deltaX * panSpeed
    camera.position.y += deltaY * panSpeed // 注意这里使用加号，保持与鼠标移动方向一致

    mouseState.value.lastX = e.clientX
    mouseState.value.lastY = e.clientY
  }
  container.addEventListener("mousemove", eventListeners.value.mousemove)

  // 鼠标松开事件
  eventListeners.value.mouseup = () => {
    mouseState.value.isDragging = false
    container.style.cursor = "grab"
  }
  container.addEventListener("mouseup", eventListeners.value.mouseup)

  // 鼠标离开容器事件
  eventListeners.value.mouseleave = () => {
    mouseState.value.isDragging = false
    container.style.cursor = "default"
  }
  container.addEventListener("mouseleave", eventListeners.value.mouseleave)

  // 鼠标滚轮事件（缩放）
  eventListeners.value.wheel = (e) => {
    console.log("wheel", e)
    e.preventDefault()
    // 传递deltaY的符号而非具体值，确保缩放方向正确
    const zoomDirection = e.deltaY > 0 ? 1 : -1
    handleZoom(zoomDirection, e.clientX, e.clientY)
  }
  container.addEventListener("wheel", eventListeners.value.wheel, { passive: false })

  // 点击事件（用于选择点）
  eventListeners.value.click = (event) => {
    // 计算鼠标在画布中的归一化坐标（-1到1之间）
    const rect = container.getBoundingClientRect()
    mouse.x = ((event.clientX - rect.left) / container.offsetWidth) * 2 - 1
    mouse.y = -((event.clientY - rect.top) / container.offsetHeight) * 2 + 1

    // 更新射线投射器
    raycaster.setFromCamera(mouse, camera)

    // 检测与轮廓点的交叉
    const intersects = raycaster.intersectObjects(lineGroup.children)

    if (intersects.length > 0) {
      // 获取点击的点的索引
      const clickedPointIndex = intersects[0].object.userData.pointIndex

      // 如果点击的是当前选中的点，则取消选中
      if (props.pointIndex === clickedPointIndex) {
        emit("update:pointIndex", -1)
      } else {
        // 否则选中当前点
        emit("update:pointIndex", clickedPointIndex)
      }
    }
  }
  container.addEventListener("click", eventListeners.value.click)

  // 设置默认鼠标样式
  container.style.cursor = "grab"
}

// 处理缩放的通用函数
const handleZoom = (zoomDirection, clientX, clientY) => {
  console.log(`output->handleZoom`, zoomDirection, clientX, clientY)
  if (!containerRef.value) return

  const container = containerRef.value

  // 获取鼠标/触摸在容器中的位置
  const rect = container.getBoundingClientRect()
  const mouseX = clientX - rect.left
  const mouseY = clientY - rect.top

  // 将鼠标位置转换为相机坐标系中的点
  const mouseNormalizedX = (mouseX / container.offsetWidth) * 2 - 1
  const mouseNormalizedY = -(mouseY / container.offsetHeight) * 2 + 1

  // 计算鼠标在世界坐标中的位置
  const vector = new THREE.Vector3(mouseNormalizedX, mouseNormalizedY, 0)
  vector.unproject(camera)

  // 计算缩放前鼠标在世界坐标中的位置
  const beforeZoomPoint = vector.clone()

  // 计算缩放因子 (正值缩小，负值放大)
  const scaleFactor = zoomDirection > 0 ? 0.9 : 1.1

  // 计算新的缩放值并应用限制
  const newZoom = camera.zoom * scaleFactor

  console.log(`output->camera.zoom`, camera.zoom)
  console.log(`output->zoomDirection`, zoomDirection)
  console.log(`output->newZoom`, newZoom)

  // 应用缩放限制，使用OrbitControls的限制
  if (newZoom < controls.minZoom || newZoom > controls.maxZoom) {
    return // 如果超出限制范围，不执行缩放
  }

  // 更新相机的缩放
  camera.zoom = newZoom
  camera.updateProjectionMatrix()

  // 重新计算缩放后鼠标在世界坐标中的位置
  const afterZoomPoint = new THREE.Vector3(mouseNormalizedX, mouseNormalizedY, 0)
  afterZoomPoint.unproject(camera)

  // 计算位置差，并移动相机以保持鼠标下的点不变
  const offset = afterZoomPoint.sub(beforeZoomPoint)
  camera.position.sub(offset)
}

// 重置视图状态
const resetView = () => {
  camera.position.set(0, 0, 10)
  camera.zoom = 1
  camera.updateProjectionMatrix()
  redrawAll()
}

// 自动适配视图，将图形居中并适合显示
const fitView = () => {
  const scaleInfo = calculateUnifiedScale()

  // 重置相机位置
  camera.position.set(0, 0, 10)
  camera.zoom = 1
  camera.updateProjectionMatrix()

  // 调整相机视野以适应内容
  const { minX, maxX, minY, maxY } = scaleInfo
  const width = maxX - minX
  const height = maxY - minY
  const aspect = containerRef.value.offsetWidth / containerRef.value.offsetHeight

  const size = Math.max(width, height) * 1.1 // 添加10%的边距

  // 计算合适的缩放比例
  const scaleX = containerRef.value.offsetWidth / width
  const scaleY = containerRef.value.offsetHeight / height
  const scale = Math.min(scaleX, scaleY) * 0.9 // 留10%边距

  // 确保缩放值在允许的范围内，使用OrbitControls的限制
  camera.zoom = Math.max(controls.minZoom, Math.min(controls.maxZoom, scale))
  camera.updateProjectionMatrix()

  // 将相机移动到数据中心
  const centerX = (minX + maxX) / 2
  const centerY = (minY + maxY) / 2
  camera.position.x = centerX
  camera.position.y = centerY

  redrawAll()
}

// 清理事件监听器
const removeMouseEventListeners = () => {
  if (!containerRef.value) return

  const container = containerRef.value

  Object.keys(eventListeners.value).forEach((key) => {
    if (eventListeners.value[key]) {
      // 对于 wheel 事件，需要传递相同的 options
      if (key === "wheel") {
        container.removeEventListener(key, eventListeners.value[key], { passive: false })
      } else {
        container.removeEventListener(key, eventListeners.value[key])
      }
      eventListeners.value[key] = null
    }
  })
}

const handleResize = () => {
  if (!renderer || !containerRef.value) return

  const width = containerRef.value.offsetWidth
  const height = containerRef.value.offsetHeight

  // 更新相机
  camera.left = -width / 2
  camera.right = width / 2
  camera.top = height / 2
  camera.bottom = -height / 2
  camera.updateProjectionMatrix()

  // 更新渲染器
  renderer.setSize(width, height)

  // 重新渲染
  redrawAll()
}

const redrawAll = () => {
  clearScene()

  // 计算统一的数据范围和缩放比例
  const scaleInfo = calculateUnifiedScale()

  // 重新绘制点数据
  if (store.boundarypointData && store.boundarypointData.length > 0) {
    renderPoint(store.boundarypointData, scaleInfo)
  }

  // 重新绘制线数据
  if (store.boundarypoint && store.boundarypoint.length > 0) {
    renderLine(store.boundarypoint, scaleInfo)
  }
}

// 计算统一的数据范围和缩放比例
const calculateUnifiedScale = () => {
  let minX = Infinity,
    maxX = -Infinity
  let minY = Infinity,
    maxY = -Infinity

  // 计算点云数据的范围
  if (store.boundarypointData && store.boundarypointData.length > 0) {
    for (let i = 0; i < store.boundarypointData.length; i += 3) {
      const x = store.boundarypointData[i]
      const y = store.boundarypointData[i + 2]
      minX = Math.min(minX, x)
      maxX = Math.max(maxX, x)
      minY = Math.min(minY, y)
      maxY = Math.max(maxY, y)
    }
  }

  // 计算轮廓点数据的范围
  if (store.boundarypoint && store.boundarypoint.length > 0) {
    const points = store.boundarypoint
      .filter((item) => item.point)
      .map((item) => {
        return item.point.split(",").map(Number)
      })

    for (let i = 0; i < points.length; i++) {
      const x = points[i][0]
      const y = points[i][1]
      minX = Math.min(minX, x)
      maxX = Math.max(maxX, x)
      minY = Math.min(minY, y)
      maxY = Math.max(maxY, y)
    }
  }

  // 如果没有数据或数据范围为0，设置默认范围
  if (minX === Infinity || maxX === -Infinity || maxX === minX) {
    minX = -1
    maxX = 1
  }
  if (minY === Infinity || maxY === -Infinity || maxY === minY) {
    minY = -1
    maxY = 1
  }

  // 计算数据的宽度和高度
  const dataWidth = maxX - minX
  const dataHeight = maxY - minY

  // 计算画布可用区域（留出90%空间给内容，10%作为边距）
  const canvasWidth = containerRef.value.offsetWidth * 0.9
  const canvasHeight = containerRef.value.offsetHeight * 0.9

  // 计算缩放比例，取较小值以确保内容完全显示
  const scaleX = canvasWidth / dataWidth
  const scaleY = canvasHeight / dataHeight
  const scale = Math.min(scaleX, scaleY)

  // 计算数据中心点
  const centerX = (minX + maxX) / 2
  const centerY = (minY + maxY) / 2

  // 不再需要设置viewState的缩放范围，已经在OrbitControls中设置

  return { minX, maxX, minY, maxY, scale, centerX, centerY }
}

function renderPoint(position, scaleInfo) {
  if (!position || position.length === 0) return

  // 清除之前的点
  clearPoints()

  const { scale, centerX, centerY } = scaleInfo

  // 创建点的几何体
  const geometry = new THREE.BufferGeometry()
  const vertices = []

  for (let i = 0; i < position.length; i += 3) {
    // 将坐标相对于中心点进行偏移，然后应用缩放
    const x = (position[i] - centerX) * scale
    // 修正y坐标，保持与原始数据一致
    const y = (position[i + 2] - centerY) * scale
    const z = 0

    vertices.push(x, y, z)
  }

  geometry.setAttribute("position", new THREE.Float32BufferAttribute(vertices, 3))

  // 创建点材质
  const material = new THREE.PointsMaterial({
    color: new THREE.Color(colors.pagePoint.normal),
    size: 2,
    sizeAttenuation: false,
    transparent: true,
    opacity: 0.7,
  })

  // 创建点对象
  const points = new THREE.Points(geometry, material)
  pointGroup.add(points)
}

function renderLine(position, scaleInfo) {
  if (!position || position.length === 0) return

  // 清除之前的线
  clearLines()

  const { scale, centerX, centerY } = scaleInfo

  const points = position.map((item) => {
    return item.point.split(",").map(Number)
  })

  if (points.length === 0) return

  // 创建轮廓线
  if (points.length > 1) {
    const lineGeometry = new THREE.BufferGeometry()
    const lineVertices = []

    // 添加所有点到线条顶点
    for (let i = 0; i < points.length; i++) {
      const x = (points[i][0] - centerX) * scale
      // 修正y坐标，保持与原始数据一致
      const y = (points[i][1] - centerY) * scale
      lineVertices.push(x, y, 0)
    }

    // 闭合线条（添加第一个点）
    lineVertices.push((points[0][0] - centerX) * scale, (points[0][1] - centerY) * scale, 0)

    lineGeometry.setAttribute("position", new THREE.Float32BufferAttribute(lineVertices, 3))

    const lineMaterial = new THREE.LineBasicMaterial({
      color: new THREE.Color(colors.line.normal),
      linewidth: 2,
    })

    const line = new THREE.Line(lineGeometry, lineMaterial)
    lineGroup.add(line)
  }

  // 创建轮廓点
  for (let i = 0; i < points.length; i++) {
    const x = (points[i][0] - centerX) * scale
    // 修正y坐标，保持与原始数据一致
    const y = (points[i][1] - centerY) * scale

    // 根据pointIndex设置圆的样式
    const isSelected = props.pointIndex === i
    const pointSize = isSelected ? 3 : 2

    // 创建点的几何体和材质
    const pointGeometry = new THREE.CircleGeometry(pointSize, 32)
    const pointMaterial = new THREE.MeshBasicMaterial({
      color: new THREE.Color(isSelected ? colors.linePoint.selected : colors.linePoint.normal),
      transparent: true,
      opacity: isSelected ? 0.8 : 0.5,
    })

    const point = new THREE.Mesh(pointGeometry, pointMaterial)
    point.position.set(x, y, 0.1) // 稍微提高z轴位置，确保点在线的上面

    // 存储点的索引，用于点击检测
    point.userData = { pointIndex: i }

    lineGroup.add(point)
  }
}

onMounted(() => {
  initScene()
  window.addEventListener("resize", handleResize)

  // 初始绘制
  redrawAll()
})

onUnmounted(() => {
  window.removeEventListener("resize", handleResize)
  removeMouseEventListeners()

  // 清理Three.js资源
  clearScene()
  if (renderer) {
    renderer.dispose()
    containerRef.value?.removeChild(renderer.domElement)
  }
  if (controls) {
    controls.dispose()
  }
})

// 监听点数据变化
watch(
  () => store.boundarypointData,
  (newData) => {
    redrawAll()
  },
  { deep: true },
)

// 监听线数据变化
watch(
  () => store.boundarypoint,
  (newData) => {
    redrawAll()
  },
  { deep: true },
)

// 监听pointIndex变化，重新绘制线条以更新圆的样式
watch(
  () => props.pointIndex,
  () => {
    redrawAll()
  },
)

defineExpose({
  resize: handleResize,
  clearCanvas: clearScene,
  redrawAll,
  resetView,
  fitView,
})
</script>
