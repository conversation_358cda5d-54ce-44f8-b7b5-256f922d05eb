<template>
  <div ref="containerRef" class="w-full h-full bg-gray-50 rounded-lg overflow-hidden"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue"
import * as THREE from "three"
// import { OrbitControls } from "@/common/js/control/OrbitControls"
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls"
import { useStore } from "@/store"

const props = defineProps({})

const store = useStore()

const containerRef = ref(null)
let scene, camera, renderer, controls
let contourGroup = new THREE.Group()

const initThreeJS = async () => {
  if (!containerRef.value) return

  const container = containerRef.value
  const rect = container.getBoundingClientRect()

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf5f5f5)

  // 创建正交相机
  const frustumSize = 50
  const aspect = rect.width / rect.height
  camera = new THREE.OrthographicCamera(
    (frustumSize * aspect) / -2, // left
    (frustumSize * aspect) / 2, // right
    frustumSize / 2, // top
    frustumSize / -2, // bottom
    0.1, // near
    1000, // far
  )
  camera.position.set(0, 0, 50)

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(rect.width, rect.height)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  container.appendChild(renderer.domElement)

  controls = new OrbitControls(camera, renderer.domElement)
  controls.target.set(0, 0, 0)
  controls.enableDamping = true
  controls.dampingFactor = 0.1

  // 开始渲染循环
  animate()
}

// 清除所有轮廓线
const clearContours = () => {
  scene.clear()
}

const render = (data) => {
  console.log("渲染轮廓数据:", data)

  // 清除之前的轮廓线
  clearContours()

  if (!data || data.length === 0) {
    console.log("没有轮廓数据")
    return
  }

  data.forEach((section, sectionIndex) => {
    const points = []
    section.boundary.forEach((boundary, boundaryIndex) => {
      const pointData = boundary.point
      points.push(new THREE.Vector3(pointData[0], pointData[1], pointData[2]))
    })
    points.push(points[0])
    // 创建几何体
    const geometry = new THREE.BufferGeometry().setFromPoints(points)

    // 创建材质 - 不同section使用不同颜色
    const material = new THREE.LineBasicMaterial({
      color: 0x64748b,
      linewidth: 2,
    })

    // 创建线条
    const line = new THREE.Line(geometry, material)
    scene.add(line)
  })
}

watch(
  () => store.sectionBoundary,
  () => {
    render(store.sectionBoundary)
  },
)

const animate = () => {
  requestAnimationFrame(animate)
  if (controls) {
    controls.update()
  }
  if (renderer && scene && camera) {
    renderer.render(scene, camera)
  }
}

const handleResize = () => {
  if (!containerRef.value || !camera || !renderer) return

  const rect = containerRef.value.getBoundingClientRect()

  // 正交相机的调整
  const frustumSize = 50
  const aspect = rect.width / rect.height
  camera.left = (frustumSize * aspect) / -2
  camera.right = (frustumSize * aspect) / 2
  camera.top = frustumSize / 2
  camera.bottom = frustumSize / -2
  camera.updateProjectionMatrix()

  renderer.setSize(rect.width, rect.height)
}

onMounted(async () => {
  await initThreeJS()
  // 初始化后尝试渲染现有数据
  if (store.sectionBoundary && store.sectionBoundary.length > 0) {
    render(store.sectionBoundary)
  }
  window.addEventListener("resize", handleResize)
})

onUnmounted(() => {
  window.removeEventListener("resize", handleResize)
  if (renderer) {
    renderer.dispose()
  }
  if (controls) {
    controls.dispose()
  }
})
defineExpose({
  resize: handleResize,
})
</script>
