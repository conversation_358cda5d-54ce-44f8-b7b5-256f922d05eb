<template>
  <div class="h-full p-4 box-border bg-gradient-to-b from-gray-50 to-gray-100">
    <div class="flex bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-4 h-full box-border space-x-4">
      <div class="w-800px flex-shrink-0 flex flex-col overflow-hidden">
        <div class="flex-1 flex overflow-hidden space-x-4">
          <div class="w-300px flex flex-col">
            <div class="mb-2 text-sm font-medium text-gray-700">剖面位置</div>
            <div class="flex-1 overflow-hidden">
              <BaseTable
                v-model:row-index="profileIndex"
                v-model:data="profilePositions"
                :disabled="!permission[0]"
                :columns="profileColumns"
                :can-add="true"
                highlight
                :custom-menu-control="customMenuControl"
                @row-click="handleProfileSelect"
                @menu-click="handleProfileAction"
                @blur="handleProfileBlur" />
            </div>
          </div>
          <div class="flex-1 overflow-hidden flex flex-col">
            <div class="mb-2 text-sm font-medium text-gray-700">轮廓点</div>
            <div class="flex-1 overflow-hidden">
              <BaseTable v-model:row-index="pointIndex" v-model:data="contourPoints" :disabled="!permission[0]" :columns="pointColumns" :can-add="isAddingPointsEnabled" highlight @menu-click="handleContourPointAction" @blur="handleContourPointBlur" />
            </div>
          </div>
        </div>
      </div>
      <div class="flex-1 flex flex-col overflow-hidden">
        <el-tabs v-model="currentView" class="h-full">
          <el-tab-pane label="二维" name="2d" class="h-full">
            <div class="h-full">
              <ContourViewer2D ref="viewer2D" v-model:point-index="pointIndex" :lazy="true" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="三维" name="3d" class="h-full" :lazy="true">
            <div class="h-full">
              <ContourViewer3D ref="viewer3D" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <AddProfileDialog v-model:visible="isAddProfileDialogVisible" @confirm="handleProfileCreate" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, unref, nextTick, watch } from "vue"
import { storeToRefs } from "pinia"
import { debounce } from "@/common/js/dataUtils"
import { useStore } from "@/store"
import BaseTable from "@/components/baseTable/index.vue"
import { ElTabs, ElTabPane } from "element-plus"
import AddProfileDialog from "./component/AddProfileDialog.vue"
import ContourViewer2D from "./component/ContourViewer2D.vue"
import ContourViewer3D from "./component/ContourViewer3D.vue"
import { createProfileColumns, createPointColumns } from "./config/tableConfig"
import { EMPTY_ROW_SYMBOL } from "@/components/baseTable/constant"
import { isEmpty } from "@/common/js/dataUtils"

const store = useStore()
const { boundarypos: profilePositions, boundarypoint: contourPoints, permission } = storeToRefs(store)

// 状态变量
const selectedProfile = ref(null) // 当前选中的剖面
const isAddingPointsEnabled = ref(false) // 是否允许添加轮廓点
const isProfileUpdateEnabled = ref(false) // 是否允许更新剖面
const isPointUpdateEnabled = ref(false) // 是否允许更新轮廓点
const isAllProfilesSelected = ref(false) // 是否全选剖面
const currentView = ref("2d") // 当前视图模式：2d/3d
const isAddProfileDialogVisible = ref(false) // 添加剖面对话框显示状态

const profileIndex = ref(-1)
const pointIndex = ref(-1)

/**
 * 计算当前选中剖面在列表中的索引
 * @returns {number} 选中剖面的索引
 */
const selectedProfileIndex = computed(() => {
  return unref(profileIndex)
})

/**
 * 处理全选/取消全选剖面
 * @param {boolean} isSelected - 是否全选
 */
const handleSelectAllProfiles = (isSelected) => {
  isAllProfilesSelected.value = isSelected
  // TODO: 实现批量更新剖面选中状态的功能
}

/**
 * 处理剖面的操作（添加/删除）
 * @param {string} actionType - 操作类型：'add'/'delete'
 * @param {Object} profileData - 剖面数据
 */
const handleProfileAction = async (actionType, { rowIndex, data, cloneData }) => {
  console.log(`output->actionType`, actionType)
  if (actionType === "add") {
    isAddProfileDialogVisible.value = true
  }
  if (actionType === "insert_up") {
    Object.assign(data, cloneData)

    await store.updateSectionBoundary({
      type: 0,
      index: [rowIndex],
      tankname: unref(store.currentTank).tankname,
      boundary: [
        {
          check: data.check,
          pos: data.pos,
          bend: data.bend,
          point: data.point,
          boundarypoint: contourPoints.value,
        },
      ],
    })
  }
  if (actionType === "insert_down") {
    await store.updateSectionBoundary({
      type: 0,
      index: [rowIndex],
      tankname: unref(store.currentTank).tankname,
      boundary: [
        {
          check: data.check,
          pos: data.pos,
          bend: data.bend,
          point: data.point,
          boundarypoint: contourPoints.value,
        },
      ],
    })
  }
  if (actionType === "delete") {
    await store.updateSectionBoundary({
      type: 2,
      index: [rowIndex],
      tankname: unref(store.currentTank).tankname,
      boundary: [],
    })
    await store.loadTankBoundary({
      tankname: unref(store.currentTank).tankname,
    })

    if (unref(selectedProfileIndex) == -1 || unref(selectedProfileIndex) == rowIndex) {
      contourPoints.value = []
      store.boundarypointData = []
      isAddingPointsEnabled.value = false
      store.loadSectionBoundary({
        index: unref(rowIndex),
        tankname: unref(store.currentTank).tankname,
      })
    }
  }
}

/**
 * 处理轮廓点的操作（删除）
 * @param {string} actionType - 操作类型：'delete'
 * @param {Object} pointData - 轮廓点数据
 */
const handleContourPointAction = async (actionType, pointData) => {
  if (actionType === "delete") {
    store.updateSectionBoundary({
      type: 1,
      index: [unref(selectedProfileIndex)],
      tankname: unref(store.currentTank).tankname,
      boundary: [
        {
          ...unref(selectedProfile),
          boundarypoint: contourPoints.value,
        },
      ],
    })
  }
}

/**
 * 处理新建剖面
 * @param {Object} profileData - 新剖面数据
 * @param {number} profileData.bend - 折点状态
 * @param {number} profileData.point - 位置
 * @param {Array} profileData.points - 轮廓点数据
 */
const handleProfileCreate = async (profileData) => {
  await store.updateSectionBoundary({
    type: 0,
    index: [],
    tankname: unref(store.currentTank).tankname,
    boundary: [
      {
        check: true,
        pos: profileData.pos,
        bend: profileData.bend,
        point: profileData.point,
        boundarypoint: profileData.points,
      },
    ],
  })
  await loadProfilePositions()
}

const handleProfileBlur = async (rowIndex) => {
  let index = unref(selectedProfileIndex)

  const rowData = unref(profilePositions)[rowIndex]

  if (unref(selectedProfileIndex) == -1) {
    index = rowIndex
    profileIndex.value = rowIndex

    await store.updateSectionBoundary({
      type: 0,
      index: [unref(profilePositions).length],
      tankname: unref(store.currentTank).tankname,
      boundary: [
        {
          ...unref(selectedProfile),
          boundarypoint: contourPoints.value,
        },
      ],
    })
    await store.loadSectionBoundary({
      index,
      tankname: unref(store.currentTank).tankname,
    })
  } else if (isEmpty(rowData.pos)) {
    unref(profilePositions).splice(rowIndex, 1)
    await store.updateSectionBoundary({
      type: 2,
      index: [rowIndex],
      tankname: unref(store.currentTank).tankname,
      boundary: [],
    })

    await store.loadTankBoundary({
      tankname: unref(store.currentTank).tankname,
    })
    await store.loadBoundaryPos({
      tankname: unref(store.currentTank).tankname,
    })
  } else {
    await store.updateSectionBoundary({
      type: 1,
      index: [index],
      tankname: unref(store.currentTank).tankname,
      boundary: [
        {
          ...unref(selectedProfile),
          boundarypoint: contourPoints.value,
        },
      ],
    })
    await store.loadSectionBoundary({
      index,
      tankname: unref(store.currentTank).tankname,
    })
  }
}

const handleContourPointBlur = async () => {
  await store.updateSectionBoundary({
    type: 3,
    index: [unref(selectedProfileIndex)],
    tankname: unref(store.currentTank).tankname,
    boundary: [
      {
        ...unref(selectedProfile),
        boundarypoint: contourPoints.value,
      },
    ],
  })
  await store.loadTankBoundary({
    tankname: unref(store.currentTank).tankname,
  })
}

/**
 * 处理剖面选择
 * @param {Object} profile - 选中的剖面数据
 */
const handleProfileSelect = async (profile) => {
  if (!profile) {
    return
  }
  if (profile.hasOwnProperty(EMPTY_ROW_SYMBOL)) {
    isAddingPointsEnabled.value = false
    contourPoints.value = []
    store.boundarypointData = []
    return
  }

  if (profile === selectedProfile.value) {
    return
  }

  isAddingPointsEnabled.value = true
  isPointUpdateEnabled.value = false
  selectedProfile.value = profile
  pointIndex.value = -1
  try {
    unref(viewer2D).clearCanvas()
    await store.loadSectionBoundary({
      index: unref(selectedProfileIndex),
      tankname: unref(store.currentTank).tankname,
    })
  } catch (error) {
    console.error(`output->error`, error)
  } finally {
    isPointUpdateEnabled.value = true
  }
}

/**
 * 处理剖面位置变化
 * @param {Object} row - 更新的剖面数据
 */
const handleProfilePositionChange = debounce(async () => {
  let index = unref(selectedProfileIndex)
  if (unref(selectedProfileIndex) == -1) {
    index = unref(profilePositions).length

    profileIndex.value = index - 1

    await store.updateSectionBoundary({
      type: 0,
      index: [unref(profilePositions).length],
      tankname: unref(store.currentTank).tankname,
      boundary: [
        {
          ...unref(selectedProfile),
          boundarypoint: contourPoints.value,
        },
      ],
    })
  } else {
    await store.updateSectionBoundary({
      type: 1,
      index: [index],
      tankname: unref(store.currentTank).tankname,
      boundary: [
        {
          ...unref(selectedProfile),
          boundarypoint: contourPoints.value,
        },
      ],
    })
  }

  await store.loadSectionBoundary({
    index,
    tankname: unref(store.currentTank).tankname,
  })
})

/**
 * 处理轮廓点变化
 * @param {Object} row - 更新的轮廓点数据
 */
const handleContourPointChange = debounce(async (row) => {
  await store.updateSectionBoundary({
    type: 3,
    index: [unref(selectedProfileIndex)],
    tankname: unref(store.currentTank).tankname,
    boundary: [
      {
        ...unref(selectedProfile),
        boundarypoint: contourPoints.value,
      },
    ],
  })
})

/**
 * 处理剖面check状态变化
 * @param {Object} row - 剖面数据
 * @param {boolean} value - 新的折点状态
 */
const handleProfileCheckChange = async (row, value) => {
  await store.updateSectionBoundary({
    type: 1,
    index: [unref(selectedProfileIndex)],
    tankname: unref(store.currentTank).tankname,
    boundary: [
      {
        ...row,
        check: value,
        boundarypoint: contourPoints.value,
      },
    ],
  })
}

/**
 * 处理剖面折点状态变化
 * @param {Object} row - 剖面数据
 * @param {boolean} value - 新的折点状态
 */
const handleProfileBendChange = async (row, value) => {
  await store.updateSectionBoundary({
    type: 1,
    index: [unref(selectedProfileIndex)],
    tankname: unref(store.currentTank).tankname,
    boundary: [
      {
        ...row,
        bend: value,
        boundarypoint: contourPoints.value,
      },
    ],
  })
}

/**
 * 处理轮廓点折点状态变化
 * @param {Object} point - 轮廓点数据
 * @param {boolean} value - 新的折点状态
 */
const handleContourPointBendChange = async (point, value) => {
  if (!selectedProfile.value) return

  const pointIndex = contourPoints.value.indexOf(point)
  const updatedPoints = [...contourPoints.value]
  updatedPoints[pointIndex] = { ...point, bend: value }

  await store.updateSectionBoundary({
    type: 1,
    index: [selectedProfileIndex.value],
    tankname: unref(store.currentTank).tankname,
    boundary: [
      {
        ...selectedProfile.value,
        boundarypoint: updatedPoints,
      },
    ],
  })
  await store.loadTankBoundary({
    tankname: unref(store.currentTank).tankname,
  })
}

/**
 * 加载剖面位置数据
 */
async function loadProfilePositions() {
  await store.loadBoundaryPos({
    tankname: store.currentTank.tankname,
  })
}

/**
 * 初始化组件
 * 加载剖面位置数据并选中第一个剖面
 */
const initialize = async () => {
  try {
    await store.calTankBoundary({
      tankname: store.currentTank.tankname,
    })
    await store.loadTankBoundary({
      tankname: unref(store.currentTank).tankname,
    })
    await loadProfilePositions()
    profileIndex.value = 0
    handleProfileSelect(profilePositions.value[0])
  } finally {
    isProfileUpdateEnabled.value = true
  }
}

const viewer2D = ref()
const viewer3D = ref()
const viewer2DTest = ref()
// 剖面表格列配置
const profileColumns = createProfileColumns(handleProfileCheckChange, handleProfileBendChange, permission)

// 轮廓点表格列配置
const pointColumns = createPointColumns(handleContourPointBendChange, permission)

const customMenuControl = (menuType, rowData, rowIndex) => {
  if (rowIndex !== selectedProfileIndex.value && ["insertUp", "insertDown"].includes(menuType)) {
    return false
  }
  return true
}

onMounted(initialize)

watch(currentView, () => {
  nextTick(() => {
    if (unref(currentView) === "2d") {
      unref(viewer2D).resize()
    } else if (unref(currentView) === "3d") {
      unref(viewer3D).resize()
    } else if (unref(currentView) === "2d-test") {
      unref(viewer2DTest).resize()
    }
  })
})
</script>
<style>
.el-tabs__content {
  flex: 1;
}
</style>
