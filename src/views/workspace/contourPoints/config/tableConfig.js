import { h, unref } from "vue"
import { ElCheckbox } from "element-plus"
import { EMPTY_ROW_SYMBOL } from "@/components/baseTable/constant"

export const createProfileColumns = (onCheckChange, onBendChange, permission) => [
  {
    label: "",
    prop: "check",
    width: 40,
    align: "center",

    render({ row }) {
      if (row.hasOwnProperty(EMPTY_ROW_SYMBOL)) return
      return h(ElCheckbox, {
        trueValue: true,
        falseValue: false,
        modelValue: row.check,
        disabled: !unref(permission)[0],
        "onUpdate:modelValue": (value) => {
          row.check = value
          onCheckChange(row, value)
        },
      })
    },
  },
  {
    label: "位置(mm)",
    prop: "pos",
    editor: {
      ctype: "number",
    },
  },
  {
    label: "折点",
    prop: "bend",
    // align: "center",
    width: 42,
    render({ row }) {
      if (row.hasOwnProperty(EMPTY_ROW_SYMBOL)) return
      return h(ElCheckbox, {
        class: "ml-2",
        modelValue: row.bend,
        disabled: !unref(permission)[0],
        "onUpdate:modelValue": (value) => {
          row.bend = value
          onBendChange(row, value)
        },
      })
    },
  },
]

export const createPointColumns = (onBendChange, permission) => [
  {
    label: "编号",
    render({ row, index }) {
      if (row.hasOwnProperty(EMPTY_ROW_SYMBOL)) return
      return h("div", {}, index + 1)
    },
  },
  {
    label: "坐标(mm)",
    prop: "point",
    editor: {
      ctype: "coordinate",
    },
  },
  {
    label: "折点",
    prop: "bend",
    width: 42,
    render({ row }) {
      if (row.hasOwnProperty(EMPTY_ROW_SYMBOL)) return
      return h(ElCheckbox, {
        class: "ml-2",
        modelValue: row.bend,
        disabled: !unref(permission)[0],
        "onUpdate:modelValue": (value) => {
          row.bend = value
          onBendChange(row, value)
        },
      })
    },
  },
]
