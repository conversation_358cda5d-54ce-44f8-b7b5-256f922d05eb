<template>
  <div class="h-full flex gap-4 p-4 bg-gray-50 box-border">
    <!-- 左侧数据区域 -->
    <div class="w-[470px] flex flex-col shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-4 box-border bg-white">
      <div class="title flex-shrink-0 pb-2">剖面数据</div>
      <div class="flex-1 overflow-hidden">
        <BaseTable :columns="tableColumns" :data="combinedData" border class="h-full" />
      </div>
    </div>
    <!-- 右侧容器 -->
    <div class="flex-1 flex flex-col gap-4 h-full">
      <!-- 顶部输入区域 -->
      <div class="bg-white shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] rounded-8px overflow-hidden p-16px box-border flex-shrink-0">
        <div class="p-2">
          <el-form :model="form" label-width="40px">
            <el-form-item label="容积" class="!mb-0">
              <el-input v-model="form.capacity" class="!w-full" placeholder="请输入舱室容积">
                <template #append>m³</template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 底部预览区域 -->
      <div class="flex-1">
        <div class="h-full bg-white shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] rounded-8px border-[16px] border-[#b6bbc1] border-solid box-border">
          <div class="w-full h-full flex items-center justify-center text-gray-400 text-sm">模型加载区域</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import BaseTable from "@/components/baseTable/index.vue"
import { cabinCapacityColumns } from "@/config/tableConfig.js"

const tableColumns = cabinCapacityColumns

// 表单数据
const form = ref({
  capacity: 0,
})

// 综合数据
const combinedData = ref(
  Array.from({ length: 50 }, (_, i) => ({
    id: String(i + 1),
    position: i + 0.1,
    area: (i * 100).toFixed(2),
    start: `(${i * 5}, ${i * 3}, ${i * 1})`,
    end: `(${i * 5 + 10}, ${i * 3 + 5}, ${i * 1 + 2})`,
  })),
)
</script>
