<template>
  <div class="h-full flex flex-col p-4 bg-gray-50 flex flex-col gap-4 box-border">
    <!-- 基本信息表格 -->
    <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-4">
      <div class="text-lg font-medium text-gray-900 mb-4">容量表</div>
      <el-form inline :disabled="true">
        <el-form-item label="起值" class="!mb-0">
          <NumberField v-model="measureData.capacitypos.spos" readonly>
            <template #append>mm</template>
          </NumberField>
        </el-form-item>
        <el-form-item label="终值" class="!mb-0">
          <NumberField v-model="measureData.capacitypos.epos" readonly>
            <template #append>mm</template>
          </NumberField>
        </el-form-item>
        <el-form-item label="增量" class="!mb-0">
          <NumberField v-model="measureData.capacitypos.step" readonly>
            <template #append>mm</template>
          </NumberField>
        </el-form-item>
      </el-form>
    </div>
    <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-4 flex-1 overflow-hidden flex flex-col box-border">
      <div class="flex-1 overflow-hidden">
        <BaseTable v-model:data="measureData.component" :disabled="true" :columns="columns" readonly></BaseTable>
      </div>
      <div class="flex justify-end text-14px text-gray-400 pt-2">总体积（m3）：{{ totalVolume }}</div>
    </div>
    <!-- 测量管配置 -->
    <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-4 flex-1 overflow-auto flex flex-col box-border">
      <div class="flex items-center mb-4">
        <div class="text-lg font-medium text-gray-900 mr-10">测量管</div>
        <el-radio-group v-model="measureType" class="flex items-center gap-6">
          <el-radio label="params">参数定义</el-radio>
          <el-radio label="points">折点定义</el-radio>
        </el-radio-group>
      </div>
      <!-- 参数定义 -->
      <div v-if="measureType === 'params'" class="space-y-6">
        <el-form :model="params" label-width="100px" class="params-form" :disabled="true">
          <div class="grid grid-cols-3">
            <el-form-item label="H" class="mb-4">
              <NumberField v-model="measureData.ruler.H" placeholder="请输入H值" class="!w-full" readonly>
                <template #append>mm</template>
              </NumberField>
            </el-form-item>
            <el-form-item label="距后" class="mb-4">
              <NumberField v-model="measureData.ruler.y" placeholder="请输入距后" class="!w-full" readonly>
                <template #append>mm</template>
              </NumberField>
            </el-form-item>
            <el-form-item label="距中" class="mb-4">
              <NumberField v-model="measureData.ruler.x" placeholder="请输入距中" class="!w-full" readonly>
                <template #append>mm</template>
              </NumberField>
            </el-form-item>
            <el-form-item label="起点" class="mb-4">
              <NumberField v-model="measureData.ruler.z" placeholder="请输入起点" class="!w-full" readonly>
                <template #append>mm</template>
              </NumberField>
            </el-form-item>
            <el-form-item label="h" class="mb-4">
              <NumberField v-model="measureData.ruler.h" placeholder="请输入h" class="!w-full" readonly>
                <template #append>mm</template>
              </NumberField>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <!-- 折点定义 -->
      <div v-else class="space-y-4 flex-1 overflow-hidden">
        <BaseTable v-model:data="measureData.ruler.point" :disabled="true" :columns="pointColumns" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue"
import BaseTable from "@/components/baseTable/index.vue"
import { formatNumber, isEmpty } from "@/common/js/dataUtils"
import NumberField from "@/components/numberField/index.vue"
const store = useStore()
const { measureData } = storeToRefs(store)

// 计算总体积
const totalVolume = computed(() => {
  if (!measureData.value?.component || !Array.isArray(measureData.value.component)) {
    return ""
  }

  const total = measureData.value.component.reduce((sum, item) => {
    const volume = Number(item.v) || 0
    return sum + volume
  }, 0)

  return formatNumber(total, 3)
})

// 基本信息表格数据
const columns = [
  {
    prop: "w1",
    label: "宽度1（mm）",
    render: ({ value }) => {
      if (value < 0) {
        return ""
      }
      return value
    },
  },
  {
    prop: "w2",
    label: "宽度2（mm）",
    render: ({ value }) => {
      if (value < 0) {
        return ""
      }
      return value
    },
  },
  {
    prop: "H",
    label: "高（mm）",
  },
  {
    prop: "L",
    label: "长（mm）",
  },
  {
    prop: "count",
    label: "件数",
  },
  {
    prop: "sz",
    label: "起点（mm）",
  },
  {
    prop: "ez",
    label: "止点（mm）",
  },
  {
    prop: "v",
    label: "体积（m³）",
  },
]

// 测量方式
const measureType = ref("params")

// 参数定义数据
const params = ref({
  heightH: "",
  distanceBack: "",
  distanceMiddle: "",
  startPoint: "",
  heightSmall: "",
})

// 折点定义表格
const pointColumns = [
  {
    prop: "x",
    label: "折点X(mm)",
  },
  {
    prop: "y",
    label: "折点Y(mm)",
  },
  {
    prop: "z",
    label: "高Z(mm)",
  },
]

store.loadMeasureData({
  tankname: store.currentTank.tankname,
})
</script>
