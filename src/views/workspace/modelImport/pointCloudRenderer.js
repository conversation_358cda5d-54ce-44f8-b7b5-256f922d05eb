import * as THREE from "three"
import { PCDLoader } from "three/examples/jsm/loaders/PCDLoader.js"
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js"
// import { OrbitControls } from "@/common/js/control/OrbitControls"
export class PointCloudRenderer {
  constructor(container) {
    if (!container) {
      throw new Error("Container is required")
    }

    this.container = container
    this.isAnimating = false
    this.animationId = null
    this.points = null
    this.onProgress = null

    // 初始化Three.js组件
    this.initScene()
    this.initCamera()
    this.initRenderer()
    this.initControls()
    // this.initLighting()
    this.initAxes()
    this.initEventListeners()

    // 开始动画循环
    this.startAnimation()
  }

  initScene() {
    this.scene = new THREE.Scene()
    this.scene.background = new THREE.Color(0xf0f0f0)
  }

  initCamera() {
    const width = this.container.clientWidth
    const height = this.container.clientHeight
    const aspect = width / height

    // 使用正交相机，设置视口大小
    const frustumSize = 10
    this.camera = new THREE.OrthographicCamera(
      -frustumSize * aspect, // left
      frustumSize * aspect, // right
      frustumSize, // top
      -frustumSize, // bottom
      0.1, // near
      1000, // far
    )
    this.camera.position.set(5, 5, 5)
    this.camera.lookAt(0, 0, 0)
  }

  initRenderer() {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance",
    })
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight)
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    this.container.appendChild(this.renderer.domElement)
  }

  initControls() {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)

    // this.renderer.domElement.addEventListener(
    //   "pointerdown",
    //   () => {
    //     this.controls.setTargetPosition(new THREE.Vector3(0, 0, 0))
    //   },
    //   true,
    // )

    // 添加控制器事件监听
    this.controls.addEventListener("change", () => {
      this.needsUpdate = true
    })
  }

  initLighting() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
    this.scene.add(ambientLight)

    // 平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(10, 10, 5)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    this.scene.add(directionalLight)

    // 点光源
    const pointLight = new THREE.PointLight(0xffffff, 0.5, 100)
    pointLight.position.set(10, 10, 10)
    this.scene.add(pointLight)
  }

  initAxes() {
    // 创建独立的坐标轴场景
    this.axesScene = new THREE.Scene()

    // 创建正交相机用于坐标轴
    const size = 100
    const aspect = this.container.clientWidth / this.container.clientHeight
    this.axesCamera = new THREE.OrthographicCamera(-size * aspect, size * aspect, size, -size, 1, 1000)
    this.axesCamera.position.set(0, 0, 100)
    this.axesCamera.lookAt(0, 0, 0)

    // 创建3D坐标轴指示器
    this.axesHelper = new THREE.AxesHelper(50)
    this.axesHelper.position.set(-50, -50, 0)
    this.axesScene.add(this.axesHelper)
  }

  initEventListeners() {
    // 防抖处理窗口大小变化
    this.handleResize = this.debounce(() => {
      if (!this.container) return

      const width = this.container.clientWidth
      const height = this.container.clientHeight

      // 更新正交相机
      const aspect = width / height
      const frustumSize = 10
      this.camera.left = -frustumSize * aspect
      this.camera.right = frustumSize * aspect
      this.camera.top = frustumSize
      this.camera.bottom = -frustumSize
      this.camera.updateProjectionMatrix()

      // 更新坐标轴相机
      const size = 100
      this.axesCamera.left = -size * aspect
      this.axesCamera.right = size * aspect
      this.axesCamera.updateProjectionMatrix()

      // 更新渲染器
      this.renderer.setSize(width, height)
      this.needsUpdate = true
    }, 100)

    window.addEventListener("resize", this.handleResize)
  }

  // 防抖函数
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  startAnimation() {
    this.isAnimating = true
    this.animate()
  }

  stopAnimation() {
    this.isAnimating = false
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  animate = () => {
    if (!this.isAnimating) return

    this.animationId = requestAnimationFrame(this.animate)

    if (this.controls) {
      this.controls.update()
    }

    this.renderer.render(this.scene, this.camera)
  }

  async loadPCD(file) {
    return new Promise((resolve, reject) => {
      if (!file) {
        reject(new Error("No file provided"))
        return
      }

      const loader = new PCDLoader()
      let fileUrl = null

      try {
        // fileUrl = URL.createObjectURL(file)

        loader.load(
          file,
          (points) => {
            console.log("points", points)
            try {
              // 清除旧的点云
              this.clearPointClouds()

              // 设置点云材质
              if (points.material) {
                points.material.size = 0.00001
                points.material.color = new THREE.Color(0x94a3b8)
                // points.material.opacity = 1
                // points.material.transparent = true
              }

              // 保存点云引用
              this.points = points

              // this.points.position.set(143, 0, -24)
              this.points.scale.multiplyScalar(0.01)
              console.log(new THREE.Box3().setFromObject(points).getCenter(new THREE.Vector3()))

              this.scene.add(points)

              // 计算统计信息
              const stats = this.calculatePointCloudStats(points)

              // 自动调整相机位置
              this.fitCameraToObject(points)

              // 清理文件URL
              if (fileUrl) {
                URL.revokeObjectURL(fileUrl)
              }

              this.needsUpdate = true
              resolve(stats)
            } catch (error) {
              if (fileUrl) {
                URL.revokeObjectURL(fileUrl)
              }
              reject(error)
            }
          },
          (xhr) => {
            if (xhr.lengthComputable && this.onProgress) {
              const progress = xhr.loaded / xhr.total
              this.onProgress(progress)
            }
          },
          (error) => {
            if (fileUrl) {
              URL.revokeObjectURL(fileUrl)
            }
            console.error("PCD loading error:", error)
            reject(new Error(`Failed to load PCD file: ${error.message || "Unknown error"}`))
          },
        )
      } catch (error) {
        if (fileUrl) {
          URL.revokeObjectURL(fileUrl)
        }
        reject(new Error(`Error creating file URL: ${error.message}`))
      }
    })
  }

  async loadPCDData(data) {
    return new Promise((resolve, reject) => {
      const loader = new PCDLoader()

      const points = loader.parse(data)
      this.clearPointClouds()

      // 设置点云材质
      if (points.material) {
        points.material.size = 0.00001
        points.material.color = new THREE.Color(0x94a3b8)
        points.material.opacity = 0.6
        points.material.transparent = true
      }

      // 保存点云引用
      this.points = points
      this.scene.add(points)

      this.fitCameraToObject(points)

      this.needsUpdate = true

      resolve()
    })
  }
  calculatePointCloudStats(points) {
    if (!points || !points.geometry) {
      return { pointCount: 0 }
    }

    const geometry = points.geometry
    const attributes = geometry.attributes

    return {
      pointCount: attributes.position ? attributes.position.count : 0,
      hasColors: !!attributes.color,
      hasNormals: !!attributes.normal,
      boundingBox: geometry.boundingBox,
    }
  }

  fitCameraToObject(object) {
    const box = new THREE.Box3().setFromObject(object)
    const center = box.getCenter(new THREE.Vector3())
    const size = box.getSize(new THREE.Vector3())

    const maxDim = Math.max(size.x, size.y, size.z)

    // 对于正交相机，调整视口大小而不是相机距离
    const frustumSize = maxDim * 0.6
    const aspect = this.container.clientWidth / this.container.clientHeight

    this.camera.left = -frustumSize * aspect
    this.camera.right = frustumSize * aspect
    this.camera.top = frustumSize
    this.camera.bottom = -frustumSize
    this.camera.updateProjectionMatrix()

    // 设置相机位置
    this.camera.position.copy(center)
    this.camera.position.z += maxDim * 2

    // 更新控制器目标
    this.controls.target.copy(center)
    this.controls.update()

    // 更新控制器距离限制
    this.controls.minDistance = maxDim * 0.1
    this.controls.maxDistance = maxDim * 5

    this.needsUpdate = true
  }

  clearPointClouds() {
    // 移除所有点云对象
    const pointClouds = this.scene.children.filter((child) => child instanceof THREE.Points)
    pointClouds.forEach((pointCloud) => {
      this.scene.remove(pointCloud)
      if (pointCloud.geometry) {
        pointCloud.geometry.dispose()
      }
      if (pointCloud.material) {
        pointCloud.material.dispose()
      }
    })
    this.points = null
  }

  dispose() {
    // 停止动画循环
    this.stopAnimation()

    // 释放场景中的对象
    this.disposeScene(this.scene)
    this.disposeScene(this.axesScene)

    // 释放控制器
    if (this.controls) {
      this.controls.removeEventListener("change")
      this.controls.dispose()
    }

    // 释放渲染器
    if (this.renderer) {
      if (this.renderer.domElement && this.renderer.domElement.parentNode) {
        this.renderer.domElement.parentNode.removeChild(this.renderer.domElement)
      }
      this.renderer.dispose()
      this.renderer.forceContextLoss()
    }

    // 移除事件监听器
    window.removeEventListener("resize", this.handleResize)

    // 清除所有引用
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.axesScene = null
    this.axesCamera = null
    this.axesHelper = null
    this.points = null
    this.container = null
    this.onProgress = null
  }

  disposeScene(scene) {
    if (!scene) return

    while (scene.children.length > 0) {
      const object = scene.children[0]

      if (object.geometry) {
        object.geometry.dispose()
      }

      if (object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach((material) => this.disposeMaterial(material))
        } else {
          this.disposeMaterial(object.material)
        }
      }

      scene.remove(object)
    }
  }

  disposeMaterial(material) {
    if (!material) return

    // 释放纹理
    Object.keys(material).forEach((key) => {
      const value = material[key]
      if (value && typeof value === "object" && value.isTexture) {
        value.dispose()
      }
    })

    material.dispose()
  }
}
