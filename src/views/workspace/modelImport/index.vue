<template>
  <div class="h-full flex box-border">
    <!-- 如果没有任何模型，显示上传界面 -->
    <div v-if="!hasModel" class="flex-1 shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] bg-[#B6BBC1]">
      <div v-if="permission[0]" class="flex h-full items-center justify-center">
        <div class="text-center rounded-lg p-12">
          <div class="upload-area" @click="handleUpload" @dragover.prevent @dragenter.prevent @drop.prevent="handleDrop" @dragleave.prevent>
            <el-icon class="mb-3 !text-4xl"><Upload /></el-icon>
            <div class="text-base text-center">本地上传模型文件</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 如果有模型，显示模型展示区域 -->
    <div v-else class="flex-1 shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] bg-[#B6BBC1] relative overflow-hidden">
      <div ref="pointCloudContainer" class="h-full w-full"></div>

      <!-- 加载进度 -->
      <div v-if="loading.show" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20">
        <div class="bg-white p-6 rounded-lg min-w-[300px]">
          <div class="text-center mb-4">{{ loading.message }}</div>
          <el-progress :percentage="loading.progress" :stroke-width="10" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount, reactive, nextTick } from "vue"
import { Upload } from "@element-plus/icons-vue"
import { PointCloudRenderer } from "./pointCloudRenderer"

const store = useStore()
const { permission } = storeToRefs(store)
const pointCloudContainer = ref(null)

const hasModel = ref(null)

// ========================== 点云渲染器相关逻辑 ==========================
// 加载状态
const loading = reactive({
  show: false,
  progress: 0,
  message: "正在加载模型...",
})

// 渲染器实例
let pointCloudRenderer = null

/**
 * 加载模型到渲染器
 * @param {File} file - 模型文件
 * @returns {Promise<Object>} - 模型统计信息
 */
const loadModel = async (file) => {
  cleanup()

  try {
    loading.show = true
    loading.progress = 0
    loading.message = "初始化渲染器..."

    await nextTick()

    if (!pointCloudContainer.value) {
      throw new Error("渲染容器未准备就绪")
    }

    pointCloudRenderer = new PointCloudRenderer(pointCloudContainer.value)

    // 设置进度回调
    pointCloudRenderer.onProgress = (progress) => {
      loading.progress = Math.round(progress * 100)
      if (progress < 0.5) {
        loading.message = "正在读取文件..."
      } else if (progress < 0.8) {
        loading.message = "正在解析点云数据..."
      } else {
        loading.message = "正在渲染模型..."
      }
    }

    const stats = await pointCloudRenderer.loadPCDData(file)

    loading.show = false
    return stats
  } catch (error) {
    loading.show = false
    console.error("模型加载失败:", error)
    throw error
  }
}

/**
 * 清理渲染器资源
 */
const cleanup = () => {
  if (pointCloudRenderer) {
    pointCloudRenderer.dispose()
    pointCloudRenderer = null
  }
}

// ========================== 文件上传相关逻辑 ==========================
/**
 * 处理点击上传
 */
const handleUpload = async () => {
  await store.updatePointCloud({
    tankname: store.currentTank.tankname,
  })

  const res = await store.loadPath(store.pointCloundPath[0])

  await loadModel(res)
  hasModel.value = true
}

onMounted(() => {
  init()
})

async function init() {
  await store.loadPointCloud({
    tankname: store.currentTank.tankname,
  })
  if (store.pointCloundPath) {
    hasModel.value = true
    const pathData = await store.loadPath(store.pointCloundPath[0], store.pointCloundUpdate)
    loadModel(pathData)
  }
}
// ========================== 生命周期 ==========================
onBeforeUnmount(() => {
  cleanup()
})
</script>

<style scoped>
.upload-area {
  @apply relative mb-4 h-48 w-48 cursor-pointer rounded-full p-8 text-white transition-all hover:opacity-90 bg-[#2680EB] flex flex-col items-center justify-center;
}

.upload-area:hover {
  @apply transform scale-105;
}

.upload-area.drag-over {
  @apply bg-[#1e5bb8] transform scale-105;
}
</style>
