<template>
  <div class="flex flex-col h-full">
    <Navbar v-model:active="active.navbar" :items="navbar" />
    <ToolBar v-model:active="active.toolBar" :items="toolBar" show-close />

    <!-- 内容区域 -->
    <div class="flex-1 overflow-auto bg-gray-50">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue"
import Navbar from "./components/navbar.vue"
import ToolBar from "@/components/toolBar/index.vue"
import { useStore } from "@/store"

const store = useStore()

const active = reactive({
  navbar: 0,
  toolBar: 0,
})

const route = useRoute()
const router = useRouter()

const tabs = ref([
  {
    icon: "close",
    label: "关闭",
    action: () => {
      store.closeTank()
      router.replace("/")
    },
  },
  { split: true },

  {
    path: "/workspace/projectInfo",
    label: "项目信息",
    icon: "projectFile",
    hideOnCustom: false,
  },
  {
    path: "/workspace/modelImport",
    label: "点云",
    icon: "modelImport",
    hideOnCustom: true,
  },
  {
    path: "/workspace/preprocessing",
    label: "补点",
    icon: "preprocess",
    hideOnCustom: true,
  },
  {
    path: "/workspace/section",
    label: "剖面定义",
    icon: "section",
    hideOnCustom: true,
  },
  {
    path: "/workspace/contourPoints",
    label: "剖面轮廓",
    icon: "outline",
    hideOnCustom: false,
  },
  {
    path: "/workspace/componentDefinition",
    label: "测量数据",
    icon: "calc",
    hideOnCustom: false,
  },
])

const { currentTank } = storeToRefs(store)
// 根据当前舱室的definetype过滤菜单项
const toolBar = computed(() => {
  const tank = currentTank.value

  // 如果没有选中舱室或者defineMethod是点云，显示所有菜单
  if (!tank || tank.definetype === 0) {
    return tabs.value
  }

  // 如果是自定义模式，隐藏标记为hideOnCustom的菜单项
  return tabs.value.filter((tab) => !tab.hideOnCustom)
})

const navbar = computed(() => {
  return toolBar.value.slice(1).filter((item) => !item.split)
})

watch(
  route,
  () => {
    active.navbar = navbar.value.findIndex((item) => item.path == route.path)
    active.toolBar = active.navbar + 2
  },
  {
    immediate: true,
  },
)
</script>
