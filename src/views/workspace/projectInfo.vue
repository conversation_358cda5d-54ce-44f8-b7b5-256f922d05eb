<template>
  <div class="flex flex-col h-full">
    <div class="flex-1 overflow-auto bg-gray-50">
      <div class="max-w-4xl mx-auto px-4 py-8">
        <!-- 项目信息卡片 -->
        <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-6 mb-6 transition-all">
          <div class="flex justify-between items-center mb-6 border-b pb-4">
            <h1 class="text-2xl font-medium text-gray-800">{{ currentTank ? currentTank.tankfullname : "未选择舱室" }}</h1>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="flex flex-col space-y-1">
              <span class="text-sm text-gray-500">舱室标识</span>
              <span class="text-base font-medium">{{ currentTank ? currentTank.tankname : "无" }}</span>
            </div>
            <div class="flex flex-col space-y-1">
              <span class="text-sm text-gray-500">舱室名称</span>
              <span class="text-base font-medium">{{ currentTank ? currentTank.tankfullname : "无" }}</span>
            </div>
            <div class="flex flex-col space-y-1">
              <span class="text-sm text-gray-500">定义方式</span>
              <span class="text-base font-medium">{{ currentTank ? (currentTank.definetype === 0 ? "点云" : "自定义") : "无" }}</span>
            </div>
            <div class="flex flex-col space-y-1">
              <span class="text-sm text-gray-500">主舱室</span>
              <span class="text-base font-medium">{{ currentTank && currentTank.roottank ? currentTank.roottank : "无" }}</span>
            </div>
            <div class="flex flex-col space-y-1">
              <span class="text-sm text-gray-500">附加方式</span>
              <span class="text-base font-medium">{{ currentTank && currentTank.addtype !== undefined ? (currentTank.addtype === 0 ? "+" : "-") : "无" }}</span>
            </div>
          </div>

          <div v-if="currentTank" class="mt-6 pt-4 border-t flex justify-end">
            <el-button v-if="permission[0]" type="primary" class="!bg-blue-500 hover:!bg-blue-600 transition-colors" @click="handleEdit">修改信息</el-button>
          </div>
        </div>

        <!-- 项目描述卡片 -->
        <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-6 transition-all">
          <h2 class="text-lg font-medium text-gray-800 mb-4 border-b pb-3">项目描述</h2>
          <div v-if="currentTank && currentTank.remark" class="text-gray-700 whitespace-pre-line">
            {{ currentTank.remark }}
          </div>
          <div v-else class="text-gray-500 italic py-2">暂无描述</div>
        </div>
      </div>
    </div>

    <!-- 编辑舱室对话框 -->
    <AddTankDialog v-model:visible="dialogVisible" :data="currentTank" @confirm="handleConfirm" />
  </div>
</template>

<script setup>
import { useRouter } from "vue-router"
import { useStore } from "@/store"
import { storeToRefs } from "pinia"
import { ref } from "vue"
import AddTankDialog from "@/views/home/<USER>/addTankDialog.vue"

const store = useStore()
const router = useRouter()
const { currentTank, tanks, permission } = storeToRefs(store)

const dialogVisible = ref(false)

// 打开编辑对话框
const handleEdit = () => {
  dialogVisible.value = true
}

// 处理确认编辑
const handleConfirm = (formData) => {
  // 更新舱室信息
  store.updateTank(formData)
}
</script>
