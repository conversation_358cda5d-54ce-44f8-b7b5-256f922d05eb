<template>
  <div class="flex bg-primary text-white select-none min-w-1024px pl-2px">
    <div
      v-for="(item, i) in items"
      :key="i"
      class="relative flex items-center w-200px h-40px bg-primary text-14px"
      :class="{
        'bg-white text-[#0069e2]': i === active,
        'text-white': active - 1 === i,
      }">
      <div class="flex items-center pl-24px" :class="{ 'pl-48px': i > 0 }">
        <SvgIcon :name="item.icon" :active="i === active" size="20" class="text-current mr-8px" />
        {{ item.label }}
      </div>

      <div
        :class="{
          'absolute top-0 right-0 h-full before:content-empty before:absolute before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rotate-45 before:w-28px before:h-28px before:border-r before:border-r-solid before:border-r-[#3d91f2] before:border-t before:border-t-solid before:border-t-[#3d91f2] before:box-border z-10':
            i != active && i != active - 1,
          'bg-white before:border-l-[#2357bf] before:border-t-0 before:border-r-0 before:rotate-0 before:w-0 before:h-0 before:border-t-20px before:border-l-20px before:border-b-20px before:border-transparent': active - 1 === i,
          'absolute top-0 right-0  h-full bg-white before:content-empty before:absolute before:top-auto before:left-auto before:border-t-solid before:border-t-[20px] before:border-t-transparent before:border-l-solid before:border-l-[20px] before:border-l-white before:border-b-solid before:border-b-[20px] before:border-b-transparent z-10 ':
            i === active,
        }"></div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  items: {
    type: Array,
    required: true,
  },
  active: {
    type: Number,
    default: 0,
  },
})
</script>
