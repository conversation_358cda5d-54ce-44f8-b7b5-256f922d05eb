<template>
  <el-dialog v-model="dialogVisible" title="导入折点" width="300px" :close-on-click-modal="false">
    <div class="h-400px overflow-auto">
      <base-table :data="tableData" :columns="columns" :selection="true" @selection-change="handleSelectionChange" />
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from "vue"
import BaseTable from "@/components/baseTable/index.vue"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  points: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(["update:visible", "confirm"])

// 对话框可见性
const dialogVisible = ref(props.visible)

// 监听对话框可见性变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
  },
)

// 监听本地对话框可见性变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit("update:visible", val)
  },
)

// 表格列配置
const columns = [
  { type: "selection" },
  {
    prop: "position",
    label: "位置(mm)",
    align: "center",
  },
]

// 表格数据
const tableData = computed(() => {
  return props.points.map((item, index) => ({
    id: index,
    position: item,
  }))
})

// 选中的数据
const selectedData = ref([])

// 选择变化处理
const handleSelectionChange = (selection) => {
  selectedData.value = selection
}

// 确认选择
const handleConfirm = () => {
  emit("confirm", selectedData.value)
}
</script>
