<template>
  <div class="p-5 h-full flex bg-gray-50 box-border gap-4">
    <div class="bg-white p-5 rounded shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] w-300px">
      <el-form :label-width="70">
        <!-- <el-form-item label="坐标选择" prop="selectedAxis">
          <el-radio-group v-model="selectedAxis">
            <el-radio label="X">X</el-radio>
            <el-radio label="Y">Y</el-radio>
            <el-radio label="Z">Z</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="起始位置" prop="startPosition">
          <NumberField v-model="startPosition" :unit="store.currentTank.unit">
            <template #append>mm</template>
          </NumberField>
        </el-form-item>
        <el-form-item label="结束位置" prop="endPosition">
          <NumberField v-model="endPosition">
            <template #append>mm</template>
          </NumberField>
        </el-form-item>
        <el-form-item label="步长" prop="step">
          <NumberField v-model="step">
            <template #append>mm</template>
          </NumberField>
        </el-form-item>
        <el-form-item v-if="permission[0]">
          <div class="flex gap-2">
            <el-button type="primary" :disabled="!isFormValid" @click="generateTableData">快速生成</el-button>
            <el-button type="success" @click="handleImportPoints">导入折点</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex-1 bg-white rounded shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-5 overflow-hidden">
      <!-- 重复错误提示 -->
      <el-alert v-if="duplicateError" :title="duplicateError.message" type="error" :closable="false" class="mb-4" />

      <base-table v-model:data="sectionpos" :disabled="!permission[0]" :columns="columns" can-add :init-row="initRow" @blur="onBlur" @menu-click="menuClick" @duplicate-error="handleDuplicateError" />
    </div>
  </div>
  <ImportPointsDialog v-model:visible="importDialogVisible" :points="bendPos" @confirm="handleImportConfirm" />
</template>

<script setup>
import { ref, computed, h, unref } from "vue"
import BaseTable from "@/components/baseTable/index.vue"
import { EMPTY_ROW_SYMBOL } from "@/components/baseTable/constant"
import ImportPointsDialog from "./components/ImportPointsDialog.vue"
import { storeToRefs } from "pinia"
import { useStore } from "@/store"
import { deepClone, debounce, isEmpty, formatNumber } from "@/common/js/dataUtils"
import NumberField from "@/components/numberField/index.vue"
const startPosition = ref(0)
const endPosition = ref(10)
const step = ref(0.1)

// 重复错误状态
const duplicateError = ref(null)

// 表格列配置
const columns = [
  {
    prop: "avg",
    label: "是否使用均值",
    width: 100,

    render({ row }) {
      if (row.hasOwnProperty(EMPTY_ROW_SYMBOL)) {
        return
      }

      return h(ElCheckbox, {
        modelValue: row.avg,
        disabled: !unref(permission)[0],
        onChange(value) {
          row.avg = value

          update(row)
        },
      })
    },
  },
  {
    prop: "pos",
    label: "位置(mm)",

    editor: {
      ctype: "number",
      checkDuplicate: true, // 启用重复检测
    },
  },
  {
    prop: "posfrom",
    label: "参考位置(mm)",

    editor: {
      ctype: "number",
      disabled(row) {
        return !row.avg
      },
    },
  },
  {
    prop: "posavg",
    label: "均值(mm)",
  },
]

const store = useStore()
const { sectionpos, permission, bendPos } = storeToRefs(store)

// 表单验证
const isFormValid = computed(() => {
  return startPosition.value !== null && endPosition.value !== null && step.value !== null && step.value > 0 && endPosition.value !== startPosition.value
})

const initRow = () => {
  return {
    pos: undefined,
    posFrom: undefined,
    posAvg: undefined,
    avg: false,
  }
}

// 生成表格数据
const generateTableData = () => {
  if (!isFormValid.value) {
    ElMessage.warning("请填写有效的起始位置、结束位置和步长")
    return
  }

  const start = Number(startPosition.value)
  const end = Number(endPosition.value)
  const stepValue = Number(step.value)

  // 确保步长正确
  if (stepValue <= 0) {
    ElMessage.warning("步长必须大于0")
    return
  }

  // 计算点的数量
  const length = Math.abs(Math.ceil((end - start) / stepValue)) + 1

  // 生成数据
  const data = []
  let currentPosition = start

  for (let i = 0; i < length; i++) {
    const pos = i === length - 1 ? end : currentPosition
    const posValue = Number(pos.toFixed(2))

    // 查找原有数据中是否存在相同pos的项
    const existingItem = sectionpos.value.find((item) => item.pos === posValue)

    if (existingItem) {
      // 如果存在，保留原有数据
      data.push({ ...existingItem })
    } else {
      // 如果不存在，创建新数据
      data.push({
        pos: posValue,
        posFrom: undefined,
        posAvg: undefined,
        avg: false,
      })
    }

    if (i < length - 1) {
      currentPosition += stepValue * (start <= end ? 1 : -1)
    }
  }

  sectionpos.value = data

  update()
}

// 导入折点对话框
const importDialogVisible = ref(false)

// 处理导入折点
const handleImportPoints = () => {
  importDialogVisible.value = true
  store.calBendPos({ tankname: store.currentTank.tankname })
}

// 处理导入确认
const handleImportConfirm = (selectedPoints) => {
  if (!selectedPoints || selectedPoints.length === 0) {
    ElMessage.warning("请至少选择一个折点")
    return
  }

  // 将选中的折点添加到当前表格数据中
  const selectedPointsData = selectedPoints.map((point) => {
    return {
      pos: Number(point.position),
      posfrom: undefined,
      posavg: undefined,
      avg: false,
    }
  })

  selectedPointsData.forEach((newItem) => {
    if (sectionpos.value.every((item) => newItem.pos != item.pos)) {
      sectionpos.value.push(newItem)
    }
  })

  sectionpos.value.sort((a, b) => {
    return a.pos - b.pos
  })

  update()

  importDialogVisible.value = false
}

const handleRowInputChange = debounce(async (row) => {
  update(row)
}, 500)

const onBlur = () => {
  update()
}

// 处理重复错误
const handleDuplicateError = (errorInfo) => {
  duplicateError.value = errorInfo

  // 3秒后自动清除错误提示
  setTimeout(() => {
    duplicateError.value = null
  }, 3000)
}
const menuClick = async (actionType, { rowIndex, data, cloneData }) => {
  if (actionType === "delete") {
    update()
  }
}

const update = async (row) => {
  if (row && row.avg && !isEmpty(row.pos) && !isEmpty(row.posfrom)) {
    const data = await store.calSectionPosAvg({
      tankname: store.currentTank.tankname,
      pos: row.pos,
      posfrom: row.posfrom,
    })

    row.posavg = formatNumber(data, 1)
  }

  sectionpos.value.sort((a, b) => a.pos - b.pos)

  const filteredSectionPos = deepClone(sectionpos.value)
    .filter((item) => {
      return item.pos !== undefined && item.pos !== ""
    })
    .map((item) => {
      if (item.avg) {
        if (isEmpty(item.posFrom) || isEmpty(item.posAvg)) {
          item.avg = false
          item.posFrom = 0
          item.posAvg = 0
        } else {
          item.posFrom = 0
          item.posAvg = 0
        }
      }
      if (isEmpty(item.posFrom)) {
        item.posFrom = 0
      }
      if (isEmpty(item.posAvg)) {
        item.posAvg = 0
      }
      return item
    })

  store.updateSectionPos({
    tankname: store.currentTank.tankname,
    sectionpos: filteredSectionPos,
  })
}

store.calTankPosAvg({
  tankname: store.currentTank.tankname,
})
store.loadSectionPos({
  tankname: store.currentTank.tankname,
})
</script>
