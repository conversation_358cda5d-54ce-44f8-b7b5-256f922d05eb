<template>
  <div class="p-4">
    <h2 class="text-xl font-bold mb-4">表格重复检测测试</h2>

    <div class="bg-white rounded shadow p-4">
      <base-table v-model:data="tableData" :columns="columns" can-add :init-row="initRow" />
    </div>

    <div class="mt-4">
      <h3 class="text-lg font-semibold mb-2">说明：</h3>
      <ul class="list-disc list-inside space-y-1 text-sm text-gray-600">
        <li>编号列配置了重复检测，输入重复值时会提示错误</li>
        <li>名称列没有配置重复检测，可以输入重复值</li>
        <li>当检测到重复时，会弹出提示框，点击确定后编辑框会重新获取焦点</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import BaseTable from "@/components/baseTable/index.vue"

// 表格数据
const tableData = ref([
  { id: 1, code: "A001", name: "项目1", description: "描述1" },
  { id: 2, code: "A002", name: "项目2", description: "描述2" },
  { id: 3, code: "A003", name: "项目3", description: "描述3" },
])

// 表格列配置
const columns = [
  {
    prop: "code",
    label: "编号",
    width: 120,
    editor: {
      ctype: "text",
      checkDuplicate: true, // 启用重复检测
    },
  },
  {
    prop: "name",
    label: "名称",
    width: 150,
    editor: {
      ctype: "text",
      // 没有配置 checkDuplicate，不检测重复
    },
  },
  {
    prop: "description",
    label: "描述",
    editor: {
      ctype: "text",
    },
  },
]

// 初始化新行
const initRow = () => {
  return {
    id: Date.now(),
    code: "",
    name: "",
    description: "",
  }
}
</script>
