<template>
  <el-dialog v-model="visible" :title="isEdit ? '编辑舱室' : '创建舱室'" width="500px" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" class="pl-2 pr-4">
      <el-form-item label="舱室标识" prop="tankname">
        <el-input v-model="form.tankname" placeholder="请输入舱室标识" :disabled="isEdit" />
      </el-form-item>
      <el-form-item label="舱室名称" prop="tankfullname">
        <el-input v-model="form.tankfullname" placeholder="请输入舱室名称" />
      </el-form-item>

      <el-form-item label="定义方式" prop="definetype">
        <el-radio-group v-model="form.definetype">
          <el-radio :label="0">点云</el-radio>
          <el-radio :label="1">自定义</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="主舱室" prop="roottank">
        <el-select v-model="form.roottank" placeholder="请选择主舱室" clearable>
          <el-option v-for="tank in filteredMainTanks" :key="tank.tankname" :label="tank.tankfullname" :value="tank.tankname" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.roottank" label="附加方式" prop="addtype">
        <el-radio-group v-model="form.addtype">
          <el-radio :label="0">+</el-radio>
          <el-radio :label="1">-</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-center">
        <el-button class="!w-100px" @click="handleCancel">取消</el-button>
        <el-button class="!w-100px !ml-10" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue"
import { useStore } from "@/store"
import { storeToRefs } from "pinia"
const store = useStore()
const { mainTanks } = storeToRefs(store)

const visible = defineModel("visible", { type: Boolean })
const emit = defineEmits(["confirm", "cancel"])

// 接收编辑的舱室数据
const props = defineProps({
  data: {
    type: Object,
    default: () => null,
  },
})

// 判断是否为编辑模式
const isEdit = computed(() => !!props.data)

// 过滤掉当前舱室的主舱室列表
const filteredMainTanks = computed(() => {
  if (!mainTanks.value) return []
  // 如果是编辑模式，过滤掉当前舱室自己
  if (isEdit.value && props.data?.tankname) {
    return mainTanks.value.filter((tank) => tank.tankname !== props.data.tankname)
  }
  return mainTanks.value
})

const formRef = ref(null)
const form = reactive({
  tankname: "", // 舱室标识
  tankfullname: "", // 舱室名称
  roottank: "", // 主舱室
  addtype: 0, // 附加方式
  definetype: 0, // 定义方式
  remark: "", // 备注
})

watch(
  () => props.data,
  (newVal) => {
    if (newVal) {
      Object.keys(form).forEach((key) => {
        if (newVal[key] !== undefined) {
          form[key] = newVal[key]
        }
      })
    } else {
      // 重置表单
      formRef.value?.resetFields()
    }
  },
  { immediate: true },
)

const rules = reactive({
  tankname: [
    { required: true, message: "请输入舱室标识", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value && (value.includes("\\") || value.includes("/"))) {
          callback(new Error('舱室标识不能包含"\\"或"/"'))
        } else if (value && !/^[a-zA-Z]/.test(value)) {
          callback(new Error("舱室标识必须以字母开头"))
        } else if (value && !/^[a-zA-Z0-9_\-]+$/.test(value)) {
          callback(new Error("舱室标识只能包含字母、数字、下划线和连字符"))
        } else {
          callback()
        }
      },
      trigger: "blur",
    },
  ],
  tankfullname: [{ required: true, message: "请输入舱室全称", trigger: "blur" }],
  addtype: [{ required: false, message: "请选择附加方式", trigger: "change" }],
})

const handleConfirm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      const formData = { ...form }

      if (!formData.roottank) {
        formData.roottank = ""
      }
      emit("confirm", formData)
      formRef.value.resetFields()
      visible.value = false
    }
  })
}

const handleCancel = () => {
  formRef.value.resetFields()
  emit("cancel")
  visible.value = false
}

const handleClose = (done) => {
  formRef.value.resetFields()
  done()
}
</script>
