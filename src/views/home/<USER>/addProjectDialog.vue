<template>
  <el-dialog v-model="visible" title="添加项目" width="500px" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" class="pl-2 pr-4">
      <el-form-item label="项目名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="项目路径" prop="path">
        <el-input v-model="form.path" placeholder="请选择项目路径" readonly>
          <template #append>
            <el-button @click="selectPath">选择</el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-center">
        <el-button class="!w-100px" @click="handleCancel">取消</el-button>
        <el-button class="!w-100px !ml-10" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from "vue"

const visible = defineModel("visible", { type: Boolean })
const emit = defineEmits(["confirm", "cancel"])

const formRef = ref(null)
const form = reactive({
  name: "",
  path: "",
})

const rules = reactive({
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value && (value.includes("\\") || value.includes("/"))) {
          callback(new Error('项目名称不能包含"\\"或"/"'))
        } else if (value && !/^[a-zA-Z]/.test(value)) {
          callback(new Error("项目名称必须以字母开头"))
        } else if (value && !/^[a-zA-Z0-9_\-]+$/.test(value)) {
          callback(new Error("项目名称只能包含字母、数字、下划线和连字符"))
        } else {
          callback()
        }
      },
      trigger: "blur",
    },
  ],
  path: [{ required: true, message: "请选择路径", trigger: "blur" }],
})

const handleConfirm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      emit("confirm", { ...form })

      formRef.value.resetFields()
      visible.value = false
    }
  })
}

const handleCancel = () => {
  formRef.value.resetFields()
  emit("cancel")
  visible.value = false
}

const handleClose = (done) => {
  formRef.value.resetFields()
  done()
}

const selectPath = () => {
  request("TC_VtoM_SelectDir", {}).then((data) => {
    form.path = data.path
  })
}
</script>
