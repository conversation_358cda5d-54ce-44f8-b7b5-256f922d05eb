<template>
  <div class="bg-primary text-white h-40px flex items-center px-4">
    <div class="flex items-center gap-2 my-4">
      <div v-for="(menu, i) in menus" :key="i" :class="{ 'bg-secondary': isActive(menu.path) }" class="flex items-center space-x-2 rounded-2 hover:bg-secondary px-3 py-2 !rounded-div whitespace-nowrap cursor-pointer" @click="navigateTo(menu.path)">
        <SvgIcon v-if="!menu.fontAwesome" :name="menu.icon" class="text-16px" />
        <SvgIcon v-else :name="menu.icon" :font-awesome="true" :fa-type="menu.faType || 'fas'" class="text-16px" />
        <span class="text-14px">{{ menu.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import { useRouter, useRoute } from "vue-router"

defineProps({})

const router = useRouter()
const route = useRoute()

const store = useStore()

const menus = computed(() => {
  if (store.open) {
    return [
      {
        label: "首页",
        icon: "projectFile",
        path: "/",
      },
      {
        label: "计算",
        icon: "calc",
        path: "/clac",
      },
      {
        label: "操作记录",
        icon: "log",
        path: "/log",
      },
    ]
  }
  return [
    {
      label: "首页",
      icon: "projectFile",
      path: "/",
    },
  ]
})

// 导航到指定路径
const navigateTo = (path) => {
  router.push(path)
}

// 判断菜单项是否激活
const isActive = (path) => {
  return route.path === path
}
</script>
