<template>
  <div class="h-full flex flex-col">
    <ToolBar :items="items" :active="-1" />

    <div class="flex flex-1 overflow-auto p-4 gap-4">
      <!-- 左侧舱室选择 -->
      <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-4 w-300px flex flex-col">
        <div class="text-lg font-medium mb-4">舱室选择</div>
        <div class="border rounded-lg overflow-hidden flex-1 tankTable">
          <BaseTable :columns="mainTankColumns" :data="mainTanks" :disabled="!permission[0]"></BaseTable>
        </div>
      </div>

      <div class="flex flex-1 flex-col gap-4">
        <!-- 船长输入 -->
        <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] p-4">
          <el-form label-position="left" :label-width="60" :disabled="!permission[0]">
            <el-form-item label="船长" class="!mb-0">
              <NumberField v-model="calculatePara.shipLength" class="!w-300px">
                <template #append>m</template>
              </NumberField>
            </el-form-item>
          </el-form>
        </div>

        <!-- 计算参数 -->
        <div class="flex-1 overflow-hidden bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] flex p-4 gap-4">
          <div class="w-400px flex-shrink-0">
            <div class="text-lg font-medium mb-4">计算参数</div>

            <el-form :model="quickCalc" label-position="left" :label-width="80" class="flex justify-around flex-col h-400px" :disabled="!permission[0]">
              <el-form-item label="计算类型">
                <el-radio-group v-model="quickCalc.calcType">
                  <el-radio value="heel">横倾</el-radio>
                  <el-radio value="trim">纵倾</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="起始值">
                <NumberField v-model="quickCalc[quickCalc.calcType].start">
                  <template #append>{{ quickCalc.calcType === "heel" ? "°" : "m" }}</template>
                </NumberField>
              </el-form-item>
              <el-form-item label="终止值">
                <NumberField v-model="quickCalc[quickCalc.calcType].end">
                  <template #append>{{ quickCalc.calcType === "heel" ? "°" : "m" }}</template>
                </NumberField>
              </el-form-item>
              <el-form-item label="步长">
                <NumberField v-model="quickCalc[quickCalc.calcType].step">
                  <template #append>{{ quickCalc.calcType === "heel" ? "°" : "m" }}</template>
                </NumberField>
              </el-form-item>

              <el-form-item>
                <el-button v-if="permission[0]" type="primary" @click="generateData">生成数据</el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 数据展示 -->
          <div class="flex-1">
            <BaseTable v-model:data="calculatePara.heel" :columns="heelColumns" :can-add="true" :disabled="!permission[0]" @blur="onBlur"></BaseTable>
          </div>
          <div class="flex-1">
            <BaseTable v-model:data="calculatePara.trim" :columns="trimColumns" :can-add="true" :disabled="!permission[0]" @blur="onBlur"></BaseTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, watch, watchEffect, nextTick, unref, h } from "vue"
import { useRouter } from "vue-router"
import { ElCheckbox, ElMessage } from "element-plus"
import { storeToRefs } from "pinia"
import ToolBar from "@/components/toolBar/index.vue"
import BaseTable from "@/components/baseTable/index.vue"
import { createDebouncedWatcher, deepClone, formatNumber, isEmpty } from "@/common/js/dataUtils"
import { useStore } from "@/store"
import Sortable from "sortablejs"
import NumberField from "@/components/numberField/index.vue"

const store = useStore()
const { mainTanks, calculatePara, permission, quickCalc } = storeToRefs(store)

// 定义菜单项
const items = computed(() => {
  return [
    {
      id: "calc",
      label: "计算",
      icon: "calc",
      show: unref(permission)[1],
      action() {
        store.calCapacity({})
      },
    },
    {
      id: "export",
      label: "导出",
      icon: "download",
      show: unref(permission)[0],
      action() {
        store.export()
      },
    },
  ].filter((item) => {
    if (item.show === false) {
      return false
    }
    return true
  })
})

// 生成数据
const generateData = () => {
  const type = quickCalc.value.calcType
  const config = quickCalc.value[type]
  const { start, end, step } = config

  // 输入验证
  if (!start || !end || !step) {
    ElMessage.warning("请填写完整的参数信息")
    return
  }

  if (step <= 0) {
    ElMessage.warning("步长必须大于0")
    return
  }

  if (start >= end) {
    ElMessage.warning("起始值必须小于终止值")
    return
  }

  const data = []
  let current = Number(start)
  while (current <= Number(end)) {
    data.push({ value: Number(current.toFixed(2)) })
    current = Number((current + Number(step)).toFixed(2))
  }

  if (type === "heel") {
    unref(calculatePara).heel = data.sort((a, b) => a.value - b.value)
  } else {
    unref(calculatePara).trim = data.sort((a, b) => a.value - b.value)
  }

  ElMessage.success("数据生成成功")
}

const all = ref(false)
// 舱室选择表格列定义
const mainTankColumns = ref([
  {
    width: 35,
    align: "center",
    renderHeader() {
      return h(ElCheckbox, {
        modelValue: all.value,
        disabled: !unref(permission)[0],
        onChange: (val) => {
          all.value = val

          mainTanks.value.forEach((item) => {
            item.check = all.value
          })

          store.updateMainTank({
            tank: mainTanks.value.map((item) => ({
              tankname: item.tankname,
              check: all.value,
            })),
          })
        },
      })
    },
    render({ row }) {
      return h(ElCheckbox, {
        modelValue: row.check,
        disabled: !unref(permission)[0],
        onChange: (val) => {
          row.check = val

          store.updateMainTank({
            tank: mainTanks.value,
          })
        },
      })
    },
  },
  { prop: "tankname", label: "舱室" },
])

// 横倾表格列定义
const heelColumns = ref([
  {
    prop: "value",
    label: "横倾(°)",
    editor: {
      ctype: "number",
    },
  },
])

// 纵倾表格列定义
const trimColumns = ref([
  {
    prop: "value",
    label: "纵倾(m)",
    editor: {
      ctype: "number",
    },
  },
])

async function load() {
  await store.loadMainTank()
  await store.loadCalculatePara()
}

load()

let lastSubmittedData = null

const update = async () => {
  const val = unref(calculatePara)

  val.heel.sort((a, b) => a.value - b.value)
  val.trim.sort((a, b) => a.value - b.value)

  const params = {
    ...val,
  }
  params.heel = val.heel.map((item) => item.value)
  params.trim = val.trim.map((item) => item.value)

  if (!lastSubmittedData) {
    lastSubmittedData = deepClone(params)
    return
  }

  await store.updateCalculatePara(params)
  lastSubmittedData = deepClone(params)
}

watch(() => calculatePara.value.shipLength, createDebouncedWatcher(update, 500), {
  deep: true,
})

const onBlur = () => {
  update()
}

const useDrag = () => {
  const { proxy } = getCurrentInstance()

  const initDrag = () => {
    nextTick(() => {
      const el = proxy.$el
      const tankTable = el.querySelector(".tankTable .el-table__body tbody")

      if (tankTable) {
        // 销毁之前的实例（如果存在）
        if (tankTable.sortableInstance) {
          tankTable.sortableInstance.destroy()
        }

        // 创建新的拖拽实例
        tankTable.sortableInstance = Sortable.create(tankTable, {
          draggable: ".el-table__row",
          animation: 150,

          onEnd: (evt) => {
            const { oldIndex, newIndex } = evt

            if (oldIndex !== newIndex) {
              // 更新数据顺序
              const tanks = [...unref(mainTanks)]
              const movedItem = tanks.splice(oldIndex, 1)[0]
              tanks.splice(newIndex, 0, movedItem)

              // 更新store中的数据
              store.updateMainTank({
                tank: tanks,
              })
            }
          },
        })
      } else {
        console.warn("未找到表格tbody元素")
      }
    })
  }

  watchEffect(() => {
    if (unref(mainTanks) && unref(mainTanks).length > 0) {
      initDrag()
    }
  })
}

useDrag()

const handleKeyup = (e) => {
  console.log(e)
  Promise.resolve().then(() => {
    if (!isEmpty(calculatePara.value.shipLength)) {
      calculatePara.value.shipLength = formatNumber(calculatePara.value.shipLength, 1)
    }
    if (!isEmpty(quickCalc.value.heel.start)) {
      quickCalc.value.heel.start = formatNumber(quickCalc.value.heel.start, 1)
    }
    if (!isEmpty(quickCalc.value.heel.end)) {
      quickCalc.value.heel.end = formatNumber(quickCalc.value.heel.end, 1)
    }
    if (!isEmpty(quickCalc.value.heel.step)) {
      quickCalc.value.heel.step = formatNumber(quickCalc.value.heel.step, 1)
    }
    if (!isEmpty(quickCalc.value.trim.start)) {
      quickCalc.value.trim.start = formatNumber(quickCalc.value.trim.start, 1)
    }
  })
}

watch(
  mainTanks,
  () => {
    if (mainTanks.value.length > 0) {
      all.value = mainTanks.value.every((item) => item.check)
    }
  },
  {
    deep: true,
  },
)
</script>

<style scoped>
/* 拖拽相关样式 */
.tankTable :deep(.sortable-ghost) {
  opacity: 0.4;
  background: #f0f9ff;
}

.tankTable :deep(.sortable-chosen) {
  background: #e0f2fe;
}

.tankTable :deep(.sortable-drag) {
  background: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tankTable :deep(tr) {
  cursor: move;
}

.tankTable :deep(tr:hover) {
  background-color: #f8fafc;
}
</style>
