<template>
  <div class="p-4 flex h-full gap-4 box-border overflow-hidden">
    <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] w-[200px] flex-shrink-0">
      <!-- 对象列表 -->
      <div class="border border-gray-200 flex flex-col">
        <div class="flex flex-col text-14px">
          <div v-for="item in recordRankList" :key="item" class="p-2 border-b border-gray-200 cursor-pointer hover:bg-gray-100" :class="{ 'bg-blue-100': selected === item }" @click="selectedItem(item)">
            {{ item }}
          </div>
          <el-empty v-if="hasTank">
            <template #image>
              <div></div>
            </template>
          </el-empty>
        </div>
      </div>
    </div>
    <div class="bg-white rounded-lg shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] flex-1 overflow-hidden">
      <!-- 操作记录表格 -->
      <base-table :data="records" :columns="columns" :context-menu-enabled="false" />
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import BaseTable from "@/components/baseTable/index.vue"

const store = useStore()
const { records, recordRankList } = storeToRefs(store)

// 对象列表数据
const objects = ref(["工程", "P1", "S1", "P2", "S2"])
const selected = ref({})

// 处理点击事件
const selectedItem = (item) => {
  selected.value = item
  store.loadRecords(item)
}

// 表格列配置
const columns = [
  { prop: "time", label: "操作时间", width: 180 },
  { prop: "operator", label: "操作人", width: 120 },
  { prop: "details", label: "操作内容" },
]

store.loadRecordTankList().then(() => {
  if (unref(recordRankList).length > 0) {
    selectedItem(unref(recordRankList)[0])
  }
})
</script>
