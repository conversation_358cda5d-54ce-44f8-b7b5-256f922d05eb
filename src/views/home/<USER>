<template>
  <div class="h-full flex flex-col">
    <ToolBar :items="items" />
    <div class="flex flex-1 overflow-auto">
      <div v-if="store.hasTank" class="container mx-auto px-4 md:px-6">
        <div class="py-6">
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
            <!-- 现有的船舱卡片 -->
            <div
              v-for="(tank, index) in tanks"
              :key="index"
              class="bg-white rounded-xl border border-gray-200 cursor-pointer transition-all duration-300 shadow-[0px_0px_3px_1px_rgba(21,69,180,0.16)] hover:border-primary hover:shadow-lg group relative overflow-hidden"
              @click="handler.goToTank(tank)">
              <!-- 上半部分：蓝色背景的图标区域 -->
              <div class="card-bg h-24 flex items-center justify-center relative">
                <div class="p-3 rounded-xl bg-white shadow-sm">
                  <svg-icon name="fas fa-ship" class="text-primary text-24px" />
                </div>

                <!-- 删除按钮 -->
                <div v-if="permission[0]" class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" @click.stop>
                  <div class="w-25px h-25px rounded-full flex items-center justify-center bg-white shadow-md hover:shadow-lg cursor-pointer hover:bg-red-50 transition-all duration-200" @click="confirmDelete(tank)">
                    <svg-icon name="fas fa-trash" class="text-red-400 text-xs" />
                  </div>
                </div>
              </div>

              <!-- 下半部分：白色背景的信息区域 -->
              <div class="p-4 bg-white">
                <!-- 船舱名称 -->
                <div class="text-center text-lg font-semibold text-gray-800 mb-3">
                  {{ tank.tankname }}
                </div>

                <!-- 操作信息 -->
                <div class="space-y-2">
                  <div v-if="tank.operatetime" class="flex items-center gap-2 text-sm text-gray-500">
                    <svg-icon name="fas fa-clock" class="text-xs opacity-70" />
                    <span>{{ tank.operatetime }}</span>
                  </div>
                  <div v-if="tank.operator" class="flex items-center gap-2 text-sm text-gray-500">
                    <svg-icon name="fas fa-user" class="text-xs opacity-70" />
                    <span>操作人：{{ tank.operator }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="text-center w-full">
        <el-empty description="暂无舱室数据"></el-empty>
      </div>
    </div>
  </div>
  <template v-if="dialog.type === 'tank'">
    <AddTankDialog v-model:visible="dialog.visible" :data="currentTank" @confirm="dialogConfirm" />
  </template>
  <template v-if="dialog.type === 'project'">
    <AddProjectDialog v-model:visible="dialog.visible" @confirm="dialogConfirm" />
  </template>
</template>

<script setup>
import ToolBar from "@/components/toolBar/index.vue"
import AddTankDialog from "./components/addTankDialog.vue"
import AddProjectDialog from "./components/addProjectDialog.vue"
import { useStore } from "@/store"
import { storeToRefs } from "pinia"
import { ElMessageBox } from "element-plus"

const store = useStore()
const router = useRouter()
const { tanks, currentTank, open, permission } = storeToRefs(store)

const dialog = reactive({
  type: "",
  visible: false,
})

// 通用对话框操作函数
const openDialog = (type, tank = null) => {
  currentTank.value = tank
  dialog.visible = true
  dialog.type = type
}

// 菜单项配置
const items = computed(() => {
  const actionButtons = []

  if (unref(open)) {
    actionButtons.push(
      {
        label: "关闭项目",
        icon: "close",

        action: async () => {
          try {
            await ElMessageBox.confirm("确定要关闭当前项目吗？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            await store.closeProject()
          } catch (error) {
            // 用户取消操作，不做任何处理
          }
        },
      },
      { split: true },
      {
        label: "创建舱室",
        icon: "new",
        show: unref(permission)[0],
        action: () => {
          store.loadMainTank()
          openDialog("tank")
        },
      },
      {
        label: "添加舱室",
        icon: "new",
        show: unref(permission)[0],
        action: () => handler.addTank(),
      },
      { split: true, show: unref(permission)[0] },

      {
        id: "import-data",
        label: "导入测量数据",
        icon: "fa-arrow-up-from-bracket",
        show: unref(permission)[0],
        action: () => handler.importMeasureData(),
      },
      {
        label: "保存",
        icon: "save",
        action: () => handler.save(),
      },
    )
  } else {
    actionButtons.push(
      {
        label: "创建项目",
        icon: "new",
        show: unref(permission)[0],
        action: () => openDialog("project"),
      },
      {
        label: "打开项目",
        icon: "new",
        action: () => handler.openProject(),
      },
      {
        id: "import-project",
        label: "导入工程",
        icon: "fa-arrow-up-from-bracket",
        show: unref(permission)[1],
        action: () => handler.importProject(),
      },
    )
  }

  return actionButtons.filter((item) => {
    if (item.show === false) {
      return false
    }

    return true
  })
})

const handler = {
  createProject: async (data) => {
    try {
      await store.newProject(data)
      ElMessage.success("项目创建成功")
    } catch (error) {
      ElMessage.error("项目创建失败")
    }
  },
  openProject: async () => {
    try {
      // window.Channel.loadPath("d://P1_.pcd")
      await store.openProject()
    } catch (error) {
      if (error.code === "3_1") {
        try {
          await ElMessageBox.confirm(error.msg, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
          store.closeProject()
        } catch (error) {
          console.log(`er->error`, error)
        }
        console.log(`er->error`, error)
      }
    }
  },

  goToTank: async (tank) => {
    await store.loadTankInfo(tank)
    await store.loadMainTank()
    router.push({
      path: "/workspace/projectInfo",
      params: {
        tankId: tank.tankid,
      },
    })
  },

  addTank: async () => {
    await store.addTank()
    await store.loadTankList()
  },
  createTank: async (data) => {
    try {
      await store.newTank(data)
      ElMessage.success("舱室创建成功")
    } catch (error) {
      console.log(`output->error`, error)
      ElMessage.error("舱室创建失败")
    }
  },
  updateTank: async (data) => {
    try {
      await store.updateTank(data)
      ElMessage.success("舱室修改成功")
    } catch (error) {
      ElMessage.error("舱室修改失败")
    }
  },
  removeTank: async (data) => {
    try {
      await store.removeTank(data)
      ElMessage.success("舱室删除成功")
    } catch (error) {
      ElMessage.error("舱室删除失败")
    }
  },
  importProject: async () => {
    await store.importProject()
  },
  importMeasureData: async () => {
    await store.importMeasureData()
    store.loadTankList()
  },
  save: async () => {
    try {
      await store.save()
      ElMessage.success("保存成功")
    } catch (error) {
      ElMessage.error(error.msg || "保存失败")
      console.error("保存失败:", error)
    }
  },
}

const dialogConfirm = async (data) => {
  if (dialog.type === "project") {
    await handler.createProject(data)
  } else if (dialog.type === "tank") {
    await handler.createTank(data)
  }

  dialog.visible = false
}

const confirmDelete = async (tank) => {
  try {
    await ElMessageBox.confirm(`确定要删除舱室 "${tank.tankname}" 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    handler.removeTank(tank)
  } catch (error) {
    // 用户取消删除，不执行任何操作
  }
}
</script>
<style>
.card-bg {
  background-image: linear-gradient(to bottom right, rgb(219, 234, 254) 0%, rgb(191, 219, 254) 100%);
}
</style>
