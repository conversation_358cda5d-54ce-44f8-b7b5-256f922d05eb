<template>
  <el-input :model-value="displayValue" v-bind="$attrs" placeholder="请输入坐标 (例如: 1,2)" @input="handleInput" @update:model-value="onInputChange" @keydown="handleKeydown" @compositionstart="handleComposition" @compositionend="handleComposition">
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData || {}"></slot>
    </template>
  </el-input>
</template>

<script setup>
import { ref, watch } from "vue"

const props = defineProps({
  decimalPlaces: {
    type: Number,
    default: 1, // 默认限制1位小数
  },
})

const modelValue = defineModel("modelValue", {
  type: String,
  default: "",
})

// 用于显示的字符串值
const displayValue = ref(modelValue.value || "")

// 监听 modelValue 变化，更新显示值
watch(
  () => modelValue.value,
  (newVal) => {
    displayValue.value = newVal || ""
  },
)

// 是否正在输入中文
const isComposing = ref(false)

// 处理中文输入法事件
const handleComposition = (event) => {
  // 安全检查 event 是否存在
  if (!event) return

  isComposing.value = event.type === "compositionstart"

  // 如果输入法结束，且输入的是非法字符（中文、中文标点、英文字母），则清空输入
  if (event.type === "compositionend" && event.data && (/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/.test(event.data) || /[a-zA-Z]/.test(event.data))) {
    const input = event.target
    const selectionStart = input?.selectionStart || 0

    // 获取当前值
    const currentValue = displayValue.value || ""

    // 计算光标前后的内容
    const beforeCursor = currentValue.substring(0, selectionStart)
    const afterCursor = currentValue.substring(selectionStart)

    // 过滤所有非法字符
    let beforeCursorFiltered = beforeCursor.replace(/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]|[a-zA-Z]/g, "")
    let afterCursorFiltered = afterCursor.replace(/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]|[a-zA-Z]/g, "")

    // 处理多个小数点和逗号
    const combinedFiltered = beforeCursorFiltered + afterCursorFiltered
    const filteredValue = filterInvalidChars(combinedFiltered)

    // 计算新的光标位置
    const newCursorPos = Math.min(beforeCursorFiltered.length, filteredValue.length)

    displayValue.value = filteredValue
    updateModelValue(filteredValue)

    // 在下一个事件循环中恢复光标位置
    setTimeout(() => {
      if (input && input.setSelectionRange) {
        input.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)

    event.preventDefault()
  }
}

// 处理输入事件
const onInputChange = (val) => {
  // 如果正在输入中文，只更新显示值但不更新模型值
  if (isComposing.value) {
    displayValue.value = val
    return
  }

  // 安全检查 val 是否为有效值
  if (val === null || val === undefined) {
    displayValue.value = ""
    updateModelValue("")
    return
  }

  // 确保 val 是字符串
  val = String(val)

  // 记录当前光标位置
  const input = document.activeElement
  const selectionStart = input?.selectionStart || 0

  // 检查是否包含非法字符（中文字符、中文标点、英文字母）
  const hasIllegalChars = /[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/.test(val) || /[a-zA-Z]/.test(val)

  if (hasIllegalChars) {
    // 过滤掉所有非法字符
    const beforeCursor = val.substring(0, selectionStart)
    const afterCursor = val.substring(selectionStart)

    let beforeCursorFiltered = beforeCursor.replace(/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]|[a-zA-Z]/g, "")
    let afterCursorFiltered = afterCursor.replace(/[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]|[a-zA-Z]/g, "")

    // 处理多个小数点和逗号
    const combinedFiltered = beforeCursorFiltered + afterCursorFiltered
    const filteredVal = filterInvalidChars(combinedFiltered)

    // 计算过滤后的光标位置
    const newCursorPos = Math.min(beforeCursorFiltered.length, filteredVal.length)

    displayValue.value = filteredVal

    // 在下一个事件循环中恢复光标位置
    setTimeout(() => {
      if (input && input.setSelectionRange) {
        input.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)

    updateModelValue(filteredVal)
  } else {
    // 即使没有非法字符，也要检查多个小数点和逗号
    const filteredVal = filterInvalidChars(val)
    if (filteredVal !== val) {
      displayValue.value = filteredVal
      updateModelValue(filteredVal)
    } else {
      displayValue.value = val
      updateModelValue(val)
    }
  }
}

// 过滤无效字符的辅助函数
const filterInvalidChars = (value) => {
  if (!value) return value

  // 处理多个逗号：只保留第一个逗号
  const firstCommaIndex = value.indexOf(",")
  if (firstCommaIndex !== -1) {
    const beforeFirstComma = value.substring(0, firstCommaIndex + 1)
    const afterFirstComma = value.substring(firstCommaIndex + 1).replace(/,/g, "")
    value = beforeFirstComma + afterFirstComma
  }

  // 处理多个小数点：在每个坐标分量中只保留第一个小数点
  const parts = value.split(",")
  const filteredParts = parts.map((part) => {
    const firstDotIndex = part.indexOf(".")
    if (firstDotIndex !== -1) {
      const beforeFirstDot = part.substring(0, firstDotIndex + 1)
      const afterFirstDot = part.substring(firstDotIndex + 1).replace(/\./g, "")
      return beforeFirstDot + afterFirstDot
    }
    return part
  })

  return filteredParts.join(",")
}

// 处理 input 事件，作为最后的防线过滤非法字符
const handleInput = (e) => {
  // 安全检查 e.target 是否存在
  if (!e || !e.target) return

  const value = e.target.value

  // 检查 value 是否为有效字符串
  if (typeof value !== "string") return

  // 检查是否包含非法字符
  const hasIllegalChars = /[a-zA-Z\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/.test(value)

  if (hasIllegalChars) {
    // 记录当前光标位置
    const selectionStart = e.target.selectionStart || 0

    // 过滤掉所有非法字符
    let filteredValue = value.replace(/[a-zA-Z\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/g, "")

    // 处理多个小数点和逗号
    filteredValue = filterInvalidChars(filteredValue)

    // 计算新的光标位置
    const beforeCursor = value.substring(0, selectionStart)
    let filteredBeforeCursor = beforeCursor.replace(/[a-zA-Z\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/g, "")
    filteredBeforeCursor = filterInvalidChars(filteredBeforeCursor)

    const newCursorPos = Math.min(filteredBeforeCursor.length, filteredValue.length)

    // 更新值
    displayValue.value = filteredValue

    // 恢复光标位置
    setTimeout(() => {
      if (e.target && e.target.setSelectionRange) {
        e.target.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)
  }
}

// 更新模型值
const updateModelValue = (val) => {
  // 处理空值
  if (val === "" || val === null || val === undefined) {
    modelValue.value = ""
    return
  }

  // 验证坐标格式是否正确
  const isValidCoordinate = /^-?\d+(\.\d*)?,-?\d+(\.\d*)?$/.test(val)

  // 更新模型值
  if (isValidCoordinate) {
    // 处理小数位数限制
    if (props.decimalPlaces > 0) {
      // 分割坐标值
      const parts = val.split(",")
      if (parts.length === 2) {
        // 处理每个坐标分量的小数位数
        const formattedParts = parts.map((part) => {
          if (part.includes(".")) {
            const [integer, decimal] = part.split(".")
            // 如果小数部分超过了限制的位数，则截取
            if (decimal.length > props.decimalPlaces) {
              return `${integer}.${decimal.substring(0, props.decimalPlaces)}`
            }
          }
          return part
        })

        // 重新组合坐标值
        const formattedValue = formattedParts.join(",")
        if (formattedValue !== val) {
          displayValue.value = formattedValue
          modelValue.value = formattedValue
          return
        }
      }
    }

    modelValue.value = val
  }
}

// 按键按下事件处理
const handleKeydown = (e) => {
  // 安全检查 e 和 e.target 是否存在
  if (!e || !e.target) return

  // 允许的功能键：Delete、Backspace、方向键、Tab键、Home、End
  const functionKeys = ["Delete", "Backspace", "ArrowLeft", "ArrowRight", "Tab", "Home", "End"]

  // 允许Ctrl/Meta组合键
  if (e.ctrlKey || e.metaKey) return

  // 允许功能键
  if (functionKeys.includes(e.key)) return

  // 检查是否为字母（包括中英文字母）
  const isLetter = /^[a-zA-Z\u4e00-\u9fa5]$/.test(e.key)

  // 如果是字母，直接阻止
  if (isLetter) {
    e.preventDefault()
    return
  }

  // 允许的字符：负号、数字、小数点、逗号
  const allowedChars = ["-", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", ".", ","]

  // 如果不是允许的字符，则阻止输入
  if (!allowedChars.includes(e.key)) {
    e.preventDefault()
    return
  }

  const value = e.target.value || ""
  const selectionStart = e.target.selectionStart || 0
  const selectionEnd = e.target.selectionEnd || 0
  const hasSelection = selectionStart !== selectionEnd

  // 如果有选中的文本，允许输入（替换选中的文本）
  if (hasSelection) return

  // 处理逗号
  if (e.key === ",") {
    // 如果已经有逗号，阻止再次输入
    if (value.includes(",")) {
      e.preventDefault()
    }
    return
  }

  // 处理负号
  if (e.key === "-") {
    const commaIndex = value.indexOf(",")

    // 负号只能出现在开头或逗号后
    if (
      // 在第一部分，但不是在开头
      (commaIndex === -1 && selectionStart !== 0) ||
      // 在第二部分，但不是紧跟逗号
      (commaIndex !== -1 && selectionStart > commaIndex && selectionStart !== commaIndex + 1) ||
      // 在第一部分前面，但已经有负号
      (selectionStart === 0 && value.startsWith("-")) ||
      // 在第二部分前面，但已经有负号
      (commaIndex !== -1 && selectionStart === commaIndex + 1 && value.substring(commaIndex + 1).startsWith("-"))
    ) {
      e.preventDefault()
    }
    return
  }

  // 处理小数点
  if (e.key === ".") {
    // 如果设置了不允许小数，则阻止输入小数点
    if (props.decimalPlaces === 0) {
      e.preventDefault()
      return
    }

    const commaIndex = value.indexOf(",")

    // 检查光标位置和是否已有小数点
    if (commaIndex === -1) {
      // 在第一部分
      if (value.includes(".")) {
        e.preventDefault()
      }
    } else if (selectionStart <= commaIndex) {
      // 在第一部分且已有小数点
      if (value.substring(0, commaIndex).includes(".")) {
        e.preventDefault()
      }
    } else {
      // 在第二部分且已有小数点
      if (value.substring(commaIndex + 1).includes(".")) {
        e.preventDefault()
      }
    }
    return
  }

  // 处理数字输入时的小数位数限制
  if (/^\d$/.test(e.key) && props.decimalPlaces > 0) {
    const commaIndex = value.indexOf(",")

    // 确定光标在哪个坐标分量中
    let currentPart
    let decimalPointIndex
    let cursorPositionInPart

    if (commaIndex === -1 || selectionStart <= commaIndex) {
      // 在第一个坐标分量
      currentPart = commaIndex === -1 ? value : value.substring(0, commaIndex)
      decimalPointIndex = currentPart.indexOf(".")
      cursorPositionInPart = selectionStart
    } else {
      // 在第二个坐标分量
      currentPart = value.substring(commaIndex + 1)
      decimalPointIndex = currentPart.indexOf(".")
      cursorPositionInPart = selectionStart - (commaIndex + 1)
    }

    // 如果有小数点，并且光标在小数点后面，检查小数位数
    if (decimalPointIndex !== -1 && cursorPositionInPart > decimalPointIndex) {
      // 计算当前小数位数
      const decimalPart = currentPart.substring(decimalPointIndex + 1)

      // 如果小数位数已达到限制，阻止输入
      if (decimalPart.length >= props.decimalPlaces) {
        e.preventDefault()
      }
    }
  }
}
</script>
