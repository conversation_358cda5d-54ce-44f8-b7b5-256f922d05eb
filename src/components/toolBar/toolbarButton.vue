<template>
  <div
    class="t flex flex-col items-center justify-center rounded transition-all duration-300 min-w-19 h-full px-1 group"
    :class="{
      'bg-[var(--el-color-primary)] text-white': active && !disabled,
      'hover:bg-gray-100': !active && !disabled,
      'cursor-pointer': !disabled,
      'cursor-not-allowed opacity-50': disabled,
    }"
    @click="handleClick">
    <div :class="{ 'text-white': active && !disabled, 'text-gray-400': disabled }" class="flex items-center justify-center mb-1.5 text-lg text-blue">
      <SvgIcon :name="icon" />
    </div>
    <span class="text-xs whitespace-nowrap leading-tight group-active:text-secondary" :class="{ 'text-gray-400': disabled }">{{ label }}</span>
  </div>
</template>

<script setup>
// Props 定义
const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  active: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

// Events 定义
const emit = defineEmits(["click"])

// 事件处理
const handleClick = () => {
  if (!props.disabled) {
    emit("click")
  }
}
</script>
