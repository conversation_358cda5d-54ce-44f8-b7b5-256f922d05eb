<template>
  <div class="relative z-1 flex flex-shrink-0 bg-white shadow h-16">
    <div class="flex items-stretch flex-1">
      <div class="flex items-center py-2 px-4 space-x-3">
        <template v-for="(item, index) in items" :key="index">
          <ToolbarButton v-if="!item.split" :icon="item.icon" :label="item.label" :disabled="item.disabled" :active="getActive(index)" @click="handler(item)" />
          <ToolbarDivider v-else />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import ToolbarButton from "./toolbarButton.vue"
import ToolbarDivider from "./toolbarDivider.vue"

const router = useRouter()

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  active: {
    type: Number,
    default: -1,
  },
})

const handler = (item) => {
  if (item.action) {
    item.action()
  }
  if (item.path) {
    router.push(item.path)
  }
}
const getActive = (index) => {
  return index === props.active
}
</script>
