<template>
  <div class="progress-bar-demo">
    <h2>全局进度条演示</h2>

    <div class="demo-section">
      <h3>基本操作</h3>
      <div class="button-group">
        <el-button type="primary" @click="showProgress">显示进度条</el-button>
        <el-button type="danger" @click="hideProgress">隐藏进度条</el-button>
        <el-button @click="resetProgress">重置</el-button>
      </div>
    </div>

    <div class="demo-section">
      <h3>手动控制进度</h3>
      <div class="control-group">
        <div class="slider-container">
          <span>进度值: {{ manualProgress }}%</span>
          <el-slider v-model="manualProgress" :min="0" :max="100" @change="updateProgress" />
        </div>
        <el-input v-model="progressText" placeholder="进度文本" @change="updateProgressText">
          <template #append>
            <el-button @click="updateProgressText">更新文本</el-button>
          </template>
        </el-input>
      </div>
    </div>

    <div class="demo-section">
      <h3>预设场景</h3>
      <div class="button-group">
        <el-button @click="simulateProgress(2000)">模拟进度 (2秒)</el-button>
        <el-button @click="simulateProgress(5000)">模拟进度 (5秒)</el-button>
        <el-button type="success" @click="completeProgress">完成进度</el-button>
        <el-button type="danger" @click="errorProgress">错误状态</el-button>
      </div>
    </div>

    <div class="demo-section">
      <h3>使用示例</h3>
      <div class="code-example">
        <pre><code>// 在任意组件中导入
import { progressBar } from '@/store/progress'

// 显示进度条
progressBar.show(0, '开始处理...')

// 更新进度
progressBar.setProgress(50, '处理中...')

// 增加进度
progressBar.increment(10)

// 模拟进度
progressBar.simulate(3000, '加载中...')

// 完成进度
progressBar.complete('处理完成')

// 错误状态
progressBar.error('处理失败')

// 隐藏进度条
progressBar.hide()</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import { progressBar } from "@/store/progress"

const manualProgress = ref(0)
const progressText = ref("")

const showProgress = () => {
  progressBar.show(manualProgress.value, progressText.value || undefined)
}

const hideProgress = () => {
  progressBar.hide()
}

const resetProgress = () => {
  progressBar.reset()
  manualProgress.value = 0
  progressText.value = ""
}

const updateProgress = (value) => {
  progressBar.setProgress(value, progressText.value || undefined)
}

const updateProgressText = () => {
  progressBar.setText(progressText.value)
}

const simulateProgress = (duration) => {
  progressBar.simulate(duration, `模拟进度 (${duration / 1000}秒)`)
}

const completeProgress = () => {
  progressBar.complete("处理完成")
}

const errorProgress = () => {
  progressBar.error("处理失败")
}
</script>

<style scoped>
.progress-bar-demo {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

h2 {
  margin-top: 0;
  margin-bottom: 24px;
  color: #333;
  font-size: 24px;
}

h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #555;
  font-size: 18px;
}

.demo-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #eee;
}

.demo-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.slider-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.code-example {
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 16px;
  overflow-x: auto;
}

pre {
  margin: 0;
  font-family: "Courier New", Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

code {
  white-space: pre-wrap;
}

@media (max-width: 640px) {
  .progress-bar-demo {
    padding: 16px;
    margin: 16px;
  }

  .button-group {
    flex-direction: column;
    gap: 8px;
  }

  .button-group .el-button {
    width: 100%;
  }
}
</style>
