<template>
  <div v-if="isVisible" class="global-progress-overlay">
    <!-- 遮罩层 -->
    <div class="overlay-backdrop"></div>

    <!-- 进度条容器 -->
    <div class="progress-modal">
      <div class="progress-content">
        <!-- 进度环 -->
        <div class="progress-circle">
          <svg class="progress-ring" width="120" height="120">
            <circle class="progress-ring-background" cx="60" cy="60" r="50" fill="transparent" stroke="#e5e7eb" stroke-width="8" />
            <circle class="progress-ring-progress" cx="60" cy="60" r="50" fill="transparent" stroke="url(#progressGradient)" stroke-width="8" stroke-linecap="round" :stroke-dasharray="circumference" :stroke-dashoffset="strokeDashoffset" />
            <defs>
              <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color: #3b82f6; stop-opacity: 1" />
                <stop offset="100%" style="stop-color: #1d4ed8; stop-opacity: 1" />
              </linearGradient>
            </defs>
          </svg>

          <!-- 进度百分比 -->
          <div class="progress-percentage">{{ Math.round(progress) }}%</div>
        </div>

        <!-- 进度文本 -->
        <div class="progress-text">
          {{ text || "处理中..." }}
        </div>

        <!-- 加载动画点 -->
        <div class="loading-dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue"
import { useProgressStore } from "@/store/progress"

const progressStore = useProgressStore()

const isVisible = computed(() => progressStore.isVisible)
const progress = computed(() => progressStore.progress)
const text = computed(() => progressStore.text)

// 计算圆形进度条的参数
const circumference = computed(() => 2 * Math.PI * 50) // 半径为50
const strokeDashoffset = computed(() => {
  return circumference.value - (progress.value / 100) * circumference.value
})
</script>

<style scoped>
.global-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  /* 移除背景模糊效果 */
}

.progress-modal {
  position: relative;
  z-index: 10000;
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 320px;
  width: 90%;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.progress-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
  filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3));
}

.progress-ring-background {
  opacity: 0.3;
}

.progress-ring-progress {
  transition: stroke-dashoffset 0.5s ease;
  animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
  0%,
  100% {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(59, 130, 246, 0.8));
  }
}

.progress-percentage {
  position: absolute;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-text {
  font-size: 16px;
  font-weight: 500;
  color: #4b5563;
  text-align: center;
  line-height: 1.5;
}

.loading-dots {
  display: flex;
  gap: 6px;
  align-items: center;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes dotBounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .progress-modal {
    padding: 32px 24px;
    max-width: 280px;
  }

  .progress-ring {
    width: 100px;
    height: 100px;
  }

  .progress-ring circle {
    r: 40;
  }

  .progress-percentage {
    font-size: 20px;
  }

  .progress-text {
    font-size: 14px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .progress-modal {
    background: #1f2937;
    color: white;
  }

  .progress-percentage {
    color: #f9fafb;
  }

  .progress-text {
    color: #d1d5db;
  }

  .progress-ring-background {
    stroke: #374151;
  }
}
</style>
