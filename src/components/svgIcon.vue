<template>
  <svg v-if="!isFontAwesome" class="svg-icon">
    <use :xlink:href="symbolId" />
  </svg>
  <font-awesome-icon v-else :icon="name" />
</template>

<script setup>
import { computed } from "vue"

const props = defineProps({
  prefix: {
    type: String,
    default: "icon",
  },
  name: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: "",
  },
  size: {
    type: [Number, String],
    default: 24,
  },
  active: {
    type: Boolean,
    default: false,
  },
})

const symbolId = computed(() => `#${props.prefix}-${props.name}`)

const isFontAwesome = computed(() => {
  return props.name.includes("fas") || props.name.includes("far") || props.name.includes("fa")
})
</script>

<style scoped>
.svg-icon {
  display: inline-block;
  overflow: hidden;
  vertical-align: -0.15em;
  fill: currentColor;
  height: 1em;
  width: 1em;
}
</style>
