<template>
  <NumberField v-if="column.editor?.ctype === 'number'" v-bind="attrs" v-model="modelValue" :disabled="disabled" />

  <CoordinateField v-else-if="column.editor?.ctype === 'coordinate'" v-bind="attrs" v-model="modelValue" :disabled="disabled" :decimal-places="column.editor?.decimalPlaces" />

  <el-select v-else-if="column.editor?.ctype === 'select'" v-bind="attrs" v-model="modelValue" placeholder="请选择" :disabled="disabled">
    <el-option v-for="option in column.editor?.options" :key="option.value" :label="option.label" :value="option.value" />
  </el-select>
  <el-input v-else v-model="modelValue" :disabled="disabled" v-bind="attrs"></el-input>
</template>

<script setup>
import NumberField from "@/components/numberField/index.vue"
import CoordinateField from "@/components/coordinateField/index.vue"
defineOptions({
  inheritAttrs: false,
})

const attrs = useAttrs()

const emit = defineEmits(["change"])
const props = defineProps({
  column: {
    type: Object,
    default: () => {},
  },
  data: {
    type: Object,
    default: () => {},
  },
})

const modelValue = defineModel({
  type: [Number, String],
})

const disabled = ref(false)
if (props.column.editor?.disabled) {
  watch(
    () => props.column.editor.disabled(props.data),
    (val) => {
      disabled.value = val
    },
    { immediate: true },
  )
}

watch(modelValue, () => {
  emit("change", props.data)
})
</script>
