import pluginVue from "eslint-plugin-vue"
import eslint<PERSON>onfigPrettier from "eslint-config-prettier"
export default [
  ...pluginVue.configs["flat/recommended"],
  eslintConfigPrettier,
  {
    languageOptions: {
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    rules: {
      "vue/multi-word-component-names": "off",
      "vue/component-tags-order": [
        "error",
        {
          order: ["template", "script", "style"],
        },
      ],
    },
  },
]
