# 部署脚本使用说明

本目录包含用于将 `dist` 目录上传到服务器并重命名为 `web` 的部署脚本。

## 脚本文件

- `upload-dist.sh` - Shell 脚本版本
- `upload-dist.js` - Node.js 脚本版本

## 功能特性

- ✅ 自动检查本地 `dist` 目录是否存在
- ✅ 测试 SSH 连接
- ✅ 如果服务器上已存在 `web` 目录，先删除再上传
- ✅ 使用 `rsync` 高效同步文件
- ✅ 上传完成后验证文件数量
- ✅ 自动设置文件权限 (755)
- ✅ 彩色输出，清晰显示执行状态

## 使用方法

### 方法一：使用 Shell 脚本

```bash
# 基本用法
./scripts/upload-dist.sh <目标路径> <服务器地址> <用户名> [端口]

# 示例
./scripts/upload-dist.sh /var/www/html server.example.com root
./scripts/upload-dist.sh /var/www/html server.example.com root 2222
```

### 方法二：使用 Node.js 脚本

```bash
# 基本用法
node scripts/upload-dist.js <目标路径> <服务器地址> <用户名> [端口]

# 示例
node scripts/upload-dist.js /var/www/html server.example.com root
node scripts/upload-dist.js /var/www/html server.example.com root 2222
```

### 方法三：使用 npm 脚本

```bash
# 使用 npm 脚本（推荐）
npm run deploy <目标路径> <服务器地址> <用户名> [端口]

# 示例
npm run deploy /var/www/html server.example.com root
npm run deploy /var/www/html server.example.com root 2222
```

## 参数说明

| 参数 | 说明 | 示例 | 必需 |
|------|------|------|------|
| 目标路径 | 服务器上的目标路径 | `/var/www/html` | ✅ |
| 服务器地址 | 服务器IP或域名 | `server.example.com` | ✅ |
| 用户名 | SSH用户名 | `root` | ✅ |
| 端口 | SSH端口 | `22` (默认) | ❌ |

## 前置条件

### 1. 本地环境
- 确保已构建项目，存在 `dist` 目录
- 安装了 `rsync` 工具
- 对于 Node.js 版本，需要 Node.js 环境

### 2. 服务器配置
- 配置了 SSH 密钥认证（推荐）或密码认证
- 用户具有目标路径的写入权限
- 服务器安装了 `rsync`

### 3. SSH 密钥配置（推荐）

```bash
# 生成 SSH 密钥对（如果还没有）
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 将公钥复制到服务器
ssh-copy-id -p 端口 用户名@服务器地址

# 测试连接
ssh -p 端口 用户名@服务器地址
```

## 完整部署流程示例

```bash
# 1. 构建项目
npm run build

# 2. 部署到服务器
npm run deploy /var/www/html server.example.com root

# 或者使用自定义端口
npm run deploy /var/www/html server.example.com root 2222
```

## 故障排除

### SSH 连接失败
- 检查服务器地址和端口是否正确
- 确认 SSH 密钥配置是否正确
- 检查防火墙设置

### 权限错误
- 确认用户对目标路径有写入权限
- 可能需要使用 `sudo` 或切换到有权限的用户

### rsync 错误
- 确认服务器安装了 `rsync`
- 检查网络连接是否稳定

### 文件数量不匹配
- 检查网络连接是否中断
- 重新运行脚本进行同步

## 注意事项

1. **备份重要数据**：脚本会删除服务器上现有的 `web` 目录
2. **网络稳定性**：确保网络连接稳定，避免上传中断
3. **权限设置**：脚本会自动设置文件权限为 755
4. **路径安全**：确保目标路径正确，避免误删重要文件 
