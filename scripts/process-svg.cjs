const fs = require("fs")
const path = require("path")

const iconsDir = path.join(__dirname, "../src/assets/icons")

// 处理单个SVG文件
function processSvg(filePath) {
  let content = fs.readFileSync(filePath, "utf8")

  // 移除颜色相关的样式定义
  content = content.replace(/<defs>.*?<\/defs>/g, "")

  // 移除class和fill属性
  content = content.replace(/\sclass="[^"]*"/g, "")
  content = content.replace(/\sfill="[^"]*"/g, "")

  // 移除style属性
  content = content.replace(/\sstyle="[^"]*"/g, "")

  // 写回文件
  fs.writeFileSync(filePath, content)
  console.log(`Processed: ${path.basename(filePath)}`)
}

// 处理目录下的所有SVG文件
fs.readdirSync(iconsDir)
  .filter((file) => file.endsWith(".svg"))
  .forEach((file) => {
    processSvg(path.join(iconsDir, file))
  })
