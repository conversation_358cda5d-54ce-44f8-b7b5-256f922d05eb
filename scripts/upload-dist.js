#!/usr/bin/env node

const { execSync, spawn } = require("child_process")
const fs = require("fs")
const path = require("path")

// 颜色定义
const colors = {
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m",
}

// 打印带颜色的消息
function printInfo(message) {
  console.log(`${colors.blue}[INFO]${colors.reset} ${message}`)
}

function printSuccess(message) {
  console.log(`${colors.green}[SUCCESS]${colors.reset} ${message}`)
}

function printWarning(message) {
  console.log(`${colors.yellow}[WARNING]${colors.reset} ${message}`)
}

function printError(message) {
  console.log(`${colors.red}[ERROR]${colors.reset} ${message}`)
}

// 显示使用说明
function showUsage() {
  console.log("使用方法:")
  console.log("  node upload-dist.js <目标路径> <服务器地址> <用户名> [端口]")
  console.log("")
  console.log("参数说明:")
  console.log("  目标路径    - 服务器上的目标路径 (例如: /var/www/html)")
  console.log("  服务器地址  - 服务器IP或域名 (例如: server.example.com)")
  console.log("  用户名      - SSH用户名 (例如: root)")
  console.log("  端口        - SSH端口 (可选，默认22)")
  console.log("")
  console.log("示例:")
  console.log("  node upload-dist.js /var/www/html server.example.com root")
  console.log("  node upload-dist.js /var/www/html server.example.com root 2222")
}

// 执行命令并返回结果
function execCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      encoding: "utf8",
      stdio: options.silent ? "pipe" : "inherit",
      ...options,
    })
    return { success: true, output: result }
  } catch (error) {
    return { success: false, error: error.message, code: error.status }
  }
}

// 异步执行命令
function execCommandAsync(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, options)
    let stdout = ""
    let stderr = ""

    child.stdout?.on("data", (data) => {
      stdout += data.toString()
      if (!options.silent) {
        process.stdout.write(data)
      }
    })

    child.stderr?.on("data", (data) => {
      stderr += data.toString()
      if (!options.silent) {
        process.stderr.write(data)
      }
    })

    child.on("close", (code) => {
      if (code === 0) {
        resolve({ success: true, stdout, stderr })
      } else {
        reject({ success: false, code, stdout, stderr })
      }
    })

    child.on("error", (error) => {
      reject({ success: false, error: error.message })
    })
  })
}

async function main() {
  // 检查参数
  const args = process.argv.slice(2)
  if (args.length < 3) {
    printError("参数不足")
    showUsage()
    process.exit(1)
  }

  const [targetPath, server, username, port = "22"] = args

  // 检查本地 dist 目录是否存在
  if (!fs.existsSync("dist")) {
    printError("本地 dist 目录不存在，请先构建项目")
    process.exit(1)
  }

  printInfo(`开始上传 dist 目录到 ${username}@${server}:${targetPath}/web`)

  try {
    // 检查 SSH 连接
    printInfo("测试 SSH 连接...")
    const sshTest = execCommand(`ssh -p ${port} -o ConnectTimeout=10 -o BatchMode=yes ${username}@${server} "echo 'SSH连接成功'"`, { silent: true })

    if (!sshTest.success) {
      printError(`无法连接到服务器 ${username}@${server}:${port}`)
      printError("请检查服务器地址、用户名、端口和SSH密钥配置")
      process.exit(1)
    }

    printSuccess("SSH 连接测试成功")

    // 检查并删除现有的 web 目录
    printInfo(`检查服务器上是否存在 ${targetPath}/web 目录...`)
    const checkDir = execCommand(`ssh -p ${port} ${username}@${server} "[ -d '${targetPath}/web' ]"`, { silent: true })

    if (checkDir.success) {
      printWarning("发现现有的 web 目录，正在删除...")
      const removeDir = execCommand(`ssh -p ${port} ${username}@${server} "rm -rf '${targetPath}/web'"`)

      if (!removeDir.success) {
        printError("删除现有 web 目录失败")
        process.exit(1)
      }
      printSuccess("现有 web 目录已删除")
    } else {
      printInfo("服务器上不存在 web 目录")
    }

    // 确保目标路径存在
    printInfo("确保目标路径存在...")
    const createPath = execCommand(`ssh -p ${port} ${username}@${server} "mkdir -p '${targetPath}'"`)

    if (!createPath.success) {
      printError("创建目标路径失败")
      process.exit(1)
    }

    // 使用 rsync 上传文件
    printInfo("开始上传文件...")
    await execCommandAsync("rsync", ["-avz", "--progress", "--delete", "-e", `ssh -p ${port}`, "dist/", `${username}@${server}:${targetPath}/web/`])

    printSuccess("文件上传完成")

    // 验证上传结果
    printInfo("验证上传结果...")
    const remoteCount = execCommand(`ssh -p ${port} ${username}@${server} "find '${targetPath}/web' -type f | wc -l"`, { silent: true })

    const localCount = execCommand("find dist -type f | wc -l", { silent: true })

    if (remoteCount.success && localCount.success) {
      const remoteFileCount = parseInt(remoteCount.output.trim())
      const localFileCount = parseInt(localCount.output.trim())

      printInfo(`本地文件数量: ${localFileCount}`)
      printInfo(`远程文件数量: ${remoteFileCount}`)

      if (remoteFileCount === localFileCount) {
        printSuccess("文件数量验证通过")
      } else {
        printWarning("文件数量不匹配，请检查上传结果")
      }
    }

    // 设置文件权限
    printInfo("设置文件权限...")
    const setPermissions = execCommand(`ssh -p ${port} ${username}@${server} "chmod -R 755 '${targetPath}/web'"`, { silent: true })

    if (!setPermissions.success) {
      printWarning("设置文件权限失败，但上传已完成")
    }

    printSuccess("部署完成！")
    printInfo(`访问路径: ${targetPath}/web`)
    printInfo("如果是 Web 服务器，请确保服务器配置正确指向该目录")
  } catch (error) {
    printError(`部署过程中发生错误: ${error.message || error}`)
    process.exit(1)
  }
}

// 运行主函数
main().catch((error) => {
  printError(`脚本执行失败: ${error.message || error}`)
  process.exit(1)
})
