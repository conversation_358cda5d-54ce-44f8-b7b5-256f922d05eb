import { defineConfig } from "vite"
import vue from "@vitejs/plugin-vue"
import legacy from "@vitejs/plugin-legacy"
import Unocss from "unocss/vite"
import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import { ElementPlusResolver } from "unplugin-vue-components/resolvers"
import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import path from "path"

export default defineConfig(({ mode }) => {
  return {
    base: "",

    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    server: {
      host: "0.0.0.0",
    },
    plugins: [
      vue(),
      legacy({
        targets: ["defaults", "not IE 11", "chrome>=64"],
      }),
      Unocss(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/],
        imports: [
          "vue",
          "vue-router",
          "pinia",
          {
            "@/common/js/request": ["request"],
            "@/store": ["useStore"],
            "@/common/js/event": [["default", "globalEvent"]],
          },
        ],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/assets/icons")],
        symbolId: "icon-[dir]-[name]",
        inject: "body-last",
        customDomId: "__svg__icons__dom__",
      }),
    ],
    build: {
      minify: false,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes("node_modules")) {
              return "vendor"
            }
          },
        },
      },
    },
  }
})
